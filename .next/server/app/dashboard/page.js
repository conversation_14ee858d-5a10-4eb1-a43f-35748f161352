/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SettingsContext.tsx */ \"(rsc)/./src/contexts/SettingsContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(rsc)/./src/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUE0SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fc4f0949cb4b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImZjNGYwOTQ5Y2I0YlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"]}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"]}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variableName_poppins___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(rsc)/./src/contexts/SettingsContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ThemeContext */ \"(rsc)/./src/contexts/ThemeContext.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: 'Invincible - Content Writing SaaS',\n    description: 'The Ultimate Content Writing SaaS Platform with AI-powered tools'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variableName_poppins___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_2__.SettingsProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useSettings: () => (/* binding */ useSettings)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useSettings = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSettings() from the server but useSettings is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx",
"useSettings",
);const SettingsProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SettingsProvider() from the server but SettingsProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx",
"SettingsProvider",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx",
"useTheme",
);const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx",
"ThemeProvider",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/SettingsContext.tsx */ \"(ssr)/./src/contexts/SettingsContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/ThemeContext.tsx */ \"(ssr)/./src/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%2C%5C%22800%5C%22%2C%5C%22900%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FSettingsContext.tsx%22%2C%22ids%22%3A%5B%22SettingsProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcontexts%2FThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUE0SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BarChart3,Clock,FileText,Mail,PenTool,Plus,Star,Target,TrendingUp,Twitter,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _components_ui_ModernCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ModernCard */ \"(ssr)/./src/components/ui/ModernCard.tsx\");\n/* harmony import */ var _components_ui_ModernButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ModernButton */ \"(ssr)/./src/components/ui/ModernButton.tsx\");\n/* harmony import */ var _components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/DashboardLayout */ \"(ssr)/./src/components/dashboard/DashboardLayout.tsx\");\n/* harmony import */ var _components_dashboard_AnalyticsWidget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/AnalyticsWidget */ \"(ssr)/./src/components/dashboard/AnalyticsWidget.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst tools = [\n    {\n        id: 'blog',\n        title: 'Blog Generator',\n        description: 'Create SEO-optimized blog posts with AI research',\n        icon: _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        href: '/blog',\n        gradient: 'from-blue-500 to-purple-600',\n        usage: '24 posts this month',\n        popular: true\n    },\n    {\n        id: 'email',\n        title: 'Email Generator',\n        description: 'Craft high-converting email campaigns',\n        icon: _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        href: '/email',\n        gradient: 'from-pink-500 to-rose-500',\n        usage: '12 emails this month',\n        popular: false\n    },\n    {\n        id: 'tweet',\n        title: 'Tweet Generator',\n        description: 'Generate viral-worthy social media content',\n        icon: _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        href: '/tweet',\n        gradient: 'from-cyan-500 to-blue-500',\n        usage: '48 tweets this month',\n        popular: false\n    },\n    {\n        id: 'youtube',\n        title: 'YouTube Scripts',\n        description: 'Create engaging video scripts with hooks',\n        icon: _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        href: '/youtube',\n        gradient: 'from-orange-500 to-red-500',\n        usage: '8 scripts this month',\n        popular: false\n    }\n];\nconst stats = [\n    {\n        title: 'Content Generated',\n        value: '92',\n        change: '+12%',\n        trend: 'up',\n        icon: _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        color: 'blue'\n    },\n    {\n        title: 'Words Written',\n        value: '45.2K',\n        change: '+8%',\n        trend: 'up',\n        icon: _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        color: 'purple'\n    },\n    {\n        title: 'Time Saved',\n        value: '24h',\n        change: '+15%',\n        trend: 'up',\n        icon: _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        color: 'green'\n    },\n    {\n        title: 'Engagement Rate',\n        value: '94%',\n        change: '+3%',\n        trend: 'up',\n        icon: _barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        color: 'orange'\n    }\n];\nconst recentContent = [\n    {\n        id: 1,\n        title: 'The Future of AI in Healthcare',\n        type: 'Blog Post',\n        createdAt: '2 hours ago',\n        status: 'Published',\n        engagement: '1.2K views'\n    },\n    {\n        id: 2,\n        title: 'Welcome to Our Newsletter',\n        type: 'Email',\n        createdAt: '5 hours ago',\n        status: 'Sent',\n        engagement: '89% open rate'\n    },\n    {\n        id: 3,\n        title: 'Top 10 AI Tools for 2024',\n        type: 'Tweet Thread',\n        createdAt: '1 day ago',\n        status: 'Published',\n        engagement: '245 likes'\n    }\n];\nfunction DashboardPage() {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"DashboardPage.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-white mb-2\",\n                                        children: \"Welcome back, John! \\uD83D\\uDC4B\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/70\",\n                                        children: \"Ready to create amazing content? Let's see what you can build today.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModernButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"primary\",\n                                size: \"lg\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 21\n                                }, void 0),\n                                children: \"Create Content\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.1\n                    },\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AnalyticsWidget__WEBPACK_IMPORTED_MODULE_6__.ContentCreatedWidget, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AnalyticsWidget__WEBPACK_IMPORTED_MODULE_6__.AvgReadTimeWidget, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AnalyticsWidget__WEBPACK_IMPORTED_MODULE_6__.ConversionRateWidget, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.15\n                    },\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AnalyticsWidget__WEBPACK_IMPORTED_MODULE_6__.ContentViewsWidget, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AnalyticsWidget__WEBPACK_IMPORTED_MODULE_6__.EngagementWidget, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.2\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AnalyticsWidget__WEBPACK_IMPORTED_MODULE_6__.AIGenerationsWidget, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.5,\n                        delay: 0.2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"Content Tools\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/tools\",\n                                    className: \"text-blue-400 hover:text-blue-300 text-sm font-medium\",\n                                    children: \"View all tools →\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                            children: tools.map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: 0.3 + index * 0.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModernCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        variant: \"elevated\",\n                                        className: \"p-6 h-full group cursor-pointer\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: tool.href,\n                                            className: \"block h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col h-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: `p-3 rounded-xl bg-gradient-to-r ${tool.gradient}`,\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(tool.icon, {\n                                                                    className: \"w-6 h-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            tool.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1 px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-xs font-medium\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Popular\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white mb-2\",\n                                                        children: tool.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/70 text-sm mb-4 flex-1\",\n                                                        children: tool.description\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white/50 text-xs\",\n                                                                children: tool.usage\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-4 h-4 text-white/50 group-hover:text-white group-hover:translate-x-1 transition-all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this)\n                                }, tool.id, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: 0.4\n                            },\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModernCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                variant: \"glass\",\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"Recent Content\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/library\",\n                                                className: \"text-blue-400 hover:text-blue-300 text-sm font-medium\",\n                                                children: \"View all →\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recentContent.map((content)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 rounded-xl bg-white/5 hover:bg-white/10 transition-colors cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: content.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white/60 text-sm\",\n                                                                        children: content.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white/50 text-sm\",\n                                                                        children: content.createdAt\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-green-400 text-sm font-medium\",\n                                                                children: content.status\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-white/60 text-sm\",\n                                                                children: content.engagement\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, content.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5,\n                                delay: 0.5\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModernCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                variant: \"glass\",\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-white mb-6\",\n                                        children: \"Quick Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModernButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"primary\",\n                                                className: \"w-full justify-start\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                children: \"Write Blog Post\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModernButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"secondary\",\n                                                className: \"w-full justify-start\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                children: \"Create Email\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModernButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"secondary\",\n                                                className: \"w-full justify-start\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                children: \"Generate Tweet\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModernButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                variant: \"secondary\",\n                                                className: \"w-full justify-start\",\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                children: \"View Analytics\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 p-4 bg-gradient-to-r from-blue-500/20 to-purple-600/20 rounded-xl border border-blue-500/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BarChart3_Clock_FileText_Mail_PenTool_Plus_Star_Target_TrendingUp_Twitter_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-400 font-medium text-sm\",\n                                                        children: \"Monthly Goal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-white text-sm mb-2\",\n                                                children: \"Content pieces: 92/100\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-white/10 rounded-full h-2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full\",\n                                                    style: {\n                                                        width: '92%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/dashboard/page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ0w7QUFDVjtBQWlCUDtBQUM4QjtBQUNJO0FBQ2E7QUFRckI7QUFFL0MsTUFBTXlCLFFBQVE7SUFDWjtRQUNFQyxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxNQUFNekIsc0tBQU9BO1FBQ2IwQixNQUFNO1FBQ05DLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxTQUFTO0lBQ1g7SUFDQTtRQUNFUCxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxNQUFNeEIsc0tBQUlBO1FBQ1Z5QixNQUFNO1FBQ05DLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxTQUFTO0lBQ1g7SUFDQTtRQUNFUCxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxNQUFNdkIsc0tBQU9BO1FBQ2J3QixNQUFNO1FBQ05DLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxTQUFTO0lBQ1g7SUFDQTtRQUNFUCxJQUFJO1FBQ0pDLE9BQU87UUFDUEMsYUFBYTtRQUNiQyxNQUFNdEIsdUtBQUtBO1FBQ1h1QixNQUFNO1FBQ05DLFVBQVU7UUFDVkMsT0FBTztRQUNQQyxTQUFTO0lBQ1g7Q0FDRDtBQUVELE1BQU1DLFFBQVE7SUFDWjtRQUNFUCxPQUFPO1FBQ1BRLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BSLE1BQU1uQix1S0FBUUE7UUFDZDRCLE9BQU87SUFDVDtJQUNBO1FBQ0VYLE9BQU87UUFDUFEsT0FBTztRQUNQQyxRQUFRO1FBQ1JDLE9BQU87UUFDUFIsTUFBTXpCLHNLQUFPQTtRQUNia0MsT0FBTztJQUNUO0lBQ0E7UUFDRVgsT0FBTztRQUNQUSxPQUFPO1FBQ1BDLFFBQVE7UUFDUkMsT0FBTztRQUNQUixNQUFNcEIsdUtBQUtBO1FBQ1g2QixPQUFPO0lBQ1Q7SUFDQTtRQUNFWCxPQUFPO1FBQ1BRLE9BQU87UUFDUEMsUUFBUTtRQUNSQyxPQUFPO1FBQ1BSLE1BQU1yQix1S0FBVUE7UUFDaEI4QixPQUFPO0lBQ1Q7Q0FDRDtBQUVELE1BQU1DLGdCQUFnQjtJQUNwQjtRQUNFYixJQUFJO1FBQ0pDLE9BQU87UUFDUGEsTUFBTTtRQUNOQyxXQUFXO1FBQ1hDLFFBQVE7UUFDUkMsWUFBWTtJQUNkO0lBQ0E7UUFDRWpCLElBQUk7UUFDSkMsT0FBTztRQUNQYSxNQUFNO1FBQ05DLFdBQVc7UUFDWEMsUUFBUTtRQUNSQyxZQUFZO0lBQ2Q7SUFDQTtRQUNFakIsSUFBSTtRQUNKQyxPQUFPO1FBQ1BhLE1BQU07UUFDTkMsV0FBVztRQUNYQyxRQUFRO1FBQ1JDLFlBQVk7SUFDZDtDQUNEO0FBRWMsU0FBU0M7SUFDdEIsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUV2Q0MsZ0RBQVNBO21DQUFDO1lBQ1I2QyxXQUFXO1FBQ2I7a0NBQUcsRUFBRTtJQUVMLElBQUksQ0FBQ0QsU0FBUztRQUNaLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDM0IsNkVBQWVBO2tCQUNkLDRFQUFDNkI7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUM5QyxrREFBTUEsQ0FBQzZDLEdBQUc7b0JBQ1RFLFNBQVM7d0JBQUVDLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUc7b0JBQzdCQyxTQUFTO3dCQUFFRixTQUFTO3dCQUFHQyxHQUFHO29CQUFFO29CQUM1QkUsWUFBWTt3QkFBRUMsVUFBVTtvQkFBSTs4QkFFNUIsNEVBQUNQO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7O2tEQUNDLDhEQUFDUTt3Q0FBR1AsV0FBVTtrREFBcUM7Ozs7OztrREFHbkQsOERBQUNRO3dDQUFFUixXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUkvQiw4REFBQy9CLG1FQUFZQTtnQ0FDWHdDLFNBQVE7Z0NBQ1JDLE1BQUs7Z0NBQ0w3QixvQkFBTSw4REFBQ2hCLHVLQUFJQTtvQ0FBQ21DLFdBQVU7Ozs7OzswQ0FDdkI7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU9MLDhEQUFDOUMsa0RBQU1BLENBQUM2QyxHQUFHO29CQUNURSxTQUFTO3dCQUFFQyxTQUFTO3dCQUFHQyxHQUFHO29CQUFHO29CQUM3QkMsU0FBUzt3QkFBRUYsU0FBUzt3QkFBR0MsR0FBRztvQkFBRTtvQkFDNUJFLFlBQVk7d0JBQUVDLFVBQVU7d0JBQUtLLE9BQU87b0JBQUk7b0JBQ3hDWCxXQUFVOztzQ0FFViw4REFBQzNCLHVGQUFvQkE7Ozs7O3NDQUNyQiw4REFBQ0Msb0ZBQWlCQTs7Ozs7c0NBQ2xCLDhEQUFDQyx1RkFBb0JBOzs7Ozs7Ozs7Ozs4QkFJdkIsOERBQUNyQixrREFBTUEsQ0FBQzZDLEdBQUc7b0JBQ1RFLFNBQVM7d0JBQUVDLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUc7b0JBQzdCQyxTQUFTO3dCQUFFRixTQUFTO3dCQUFHQyxHQUFHO29CQUFFO29CQUM1QkUsWUFBWTt3QkFBRUMsVUFBVTt3QkFBS0ssT0FBTztvQkFBSztvQkFDekNYLFdBQVU7O3NDQUVWLDhEQUFDN0IscUZBQWtCQTs7Ozs7c0NBQ25CLDhEQUFDQyxtRkFBZ0JBOzs7Ozs7Ozs7Ozs4QkFJbkIsOERBQUNsQixrREFBTUEsQ0FBQzZDLEdBQUc7b0JBQ1RFLFNBQVM7d0JBQUVDLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUc7b0JBQzdCQyxTQUFTO3dCQUFFRixTQUFTO3dCQUFHQyxHQUFHO29CQUFFO29CQUM1QkUsWUFBWTt3QkFBRUMsVUFBVTt3QkFBS0ssT0FBTztvQkFBSTs4QkFFeEMsNEVBQUNuQyxzRkFBbUJBOzs7Ozs7Ozs7OzhCQUl0Qiw4REFBQ3RCLGtEQUFNQSxDQUFDNkMsR0FBRztvQkFDVEUsU0FBUzt3QkFBRUMsU0FBUzt3QkFBR0MsR0FBRztvQkFBRztvQkFDN0JDLFNBQVM7d0JBQUVGLFNBQVM7d0JBQUdDLEdBQUc7b0JBQUU7b0JBQzVCRSxZQUFZO3dCQUFFQyxVQUFVO3dCQUFLSyxPQUFPO29CQUFJOztzQ0FFeEMsOERBQUNaOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ1k7b0NBQUdaLFdBQVU7OENBQWdDOzs7Ozs7OENBQzlDLDhEQUFDN0Msa0RBQUlBO29DQUFDMkIsTUFBSztvQ0FBU2tCLFdBQVU7OENBQXdEOzs7Ozs7Ozs7Ozs7c0NBS3hGLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWnZCLE1BQU1vQyxHQUFHLENBQUMsQ0FBQ0MsTUFBTUMsc0JBQ2hCLDhEQUFDN0Qsa0RBQU1BLENBQUM2QyxHQUFHO29DQUVURSxTQUFTO3dDQUFFQyxTQUFTO3dDQUFHQyxHQUFHO29DQUFHO29DQUM3QkMsU0FBUzt3Q0FBRUYsU0FBUzt3Q0FBR0MsR0FBRztvQ0FBRTtvQ0FDNUJFLFlBQVk7d0NBQUVDLFVBQVU7d0NBQUtLLE9BQU8sTUFBTUksUUFBUTtvQ0FBSTs4Q0FFdEQsNEVBQUMvQyxpRUFBVUE7d0NBQUN5QyxTQUFRO3dDQUFXVCxXQUFVO2tEQUN2Qyw0RUFBQzdDLGtEQUFJQTs0Q0FBQzJCLE1BQU1nQyxLQUFLaEMsSUFBSTs0Q0FBRWtCLFdBQVU7c0RBQy9CLDRFQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVcsQ0FBQyxnQ0FBZ0MsRUFBRWMsS0FBSy9CLFFBQVEsRUFBRTswRUFDaEUsNEVBQUMrQixLQUFLakMsSUFBSTtvRUFBQ21CLFdBQVU7Ozs7Ozs7Ozs7OzREQUV0QmMsS0FBSzdCLE9BQU8sa0JBQ1gsOERBQUNjO2dFQUFJQyxXQUFVOztrRkFDYiw4REFBQ2xDLHVLQUFJQTt3RUFBQ2tDLFdBQVU7Ozs7OztvRUFBWTs7Ozs7Ozs7Ozs7OztrRUFNbEMsOERBQUNnQjt3REFBR2hCLFdBQVU7a0VBQXlDYyxLQUFLbkMsS0FBSzs7Ozs7O2tFQUNqRSw4REFBQzZCO3dEQUFFUixXQUFVO2tFQUFxQ2MsS0FBS2xDLFdBQVc7Ozs7OztrRUFFbEUsOERBQUNtQjt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNpQjtnRUFBS2pCLFdBQVU7MEVBQXlCYyxLQUFLOUIsS0FBSzs7Ozs7OzBFQUNuRCw4REFBQ3BCLHVLQUFVQTtnRUFBQ29DLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBekJ6QmMsS0FBS3BDLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBb0NwQiw4REFBQ3FCO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQzlDLGtEQUFNQSxDQUFDNkMsR0FBRzs0QkFDVEUsU0FBUztnQ0FBRUMsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRzs0QkFDN0JDLFNBQVM7Z0NBQUVGLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUU7NEJBQzVCRSxZQUFZO2dDQUFFQyxVQUFVO2dDQUFLSyxPQUFPOzRCQUFJOzRCQUN4Q1gsV0FBVTtzQ0FFViw0RUFBQ2hDLGlFQUFVQTtnQ0FBQ3lDLFNBQVE7Z0NBQVFULFdBQVU7O2tEQUNwQyw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDZ0I7Z0RBQUdoQixXQUFVOzBEQUFtQzs7Ozs7OzBEQUNqRCw4REFBQzdDLGtEQUFJQTtnREFBQzJCLE1BQUs7Z0RBQVdrQixXQUFVOzBEQUF3RDs7Ozs7Ozs7Ozs7O2tEQUsxRiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1pULGNBQWNzQixHQUFHLENBQUMsQ0FBQ0ssd0JBQ2xCLDhEQUFDbkI7Z0RBRUNDLFdBQVU7O2tFQUVWLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNtQjtnRUFBR25CLFdBQVU7MEVBQTBCa0IsUUFBUXZDLEtBQUs7Ozs7OzswRUFDckQsOERBQUNvQjtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNpQjt3RUFBS2pCLFdBQVU7a0ZBQXlCa0IsUUFBUTFCLElBQUk7Ozs7OztrRkFDckQsOERBQUN5Qjt3RUFBS2pCLFdBQVU7a0ZBQXlCa0IsUUFBUXpCLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRUFHOUQsOERBQUNNO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQXNDa0IsUUFBUXhCLE1BQU07Ozs7OzswRUFDbkUsOERBQUNLO2dFQUFJQyxXQUFVOzBFQUF5QmtCLFFBQVF2QixVQUFVOzs7Ozs7Ozs7Ozs7OytDQVp2RHVCLFFBQVF4QyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBcUJ6Qiw4REFBQ3hCLGtEQUFNQSxDQUFDNkMsR0FBRzs0QkFDVEUsU0FBUztnQ0FBRUMsU0FBUztnQ0FBR0MsR0FBRzs0QkFBRzs0QkFDN0JDLFNBQVM7Z0NBQUVGLFNBQVM7Z0NBQUdDLEdBQUc7NEJBQUU7NEJBQzVCRSxZQUFZO2dDQUFFQyxVQUFVO2dDQUFLSyxPQUFPOzRCQUFJO3NDQUV4Qyw0RUFBQzNDLGlFQUFVQTtnQ0FBQ3lDLFNBQVE7Z0NBQVFULFdBQVU7O2tEQUNwQyw4REFBQ2dCO3dDQUFHaEIsV0FBVTtrREFBd0M7Ozs7OztrREFFdEQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQy9CLG1FQUFZQTtnREFDWHdDLFNBQVE7Z0RBQ1JULFdBQVU7Z0RBQ1ZuQixvQkFBTSw4REFBQ3pCLHNLQUFPQTtvREFBQzRDLFdBQVU7Ozs7OzswREFDMUI7Ozs7OzswREFHRCw4REFBQy9CLG1FQUFZQTtnREFDWHdDLFNBQVE7Z0RBQ1JULFdBQVU7Z0RBQ1ZuQixvQkFBTSw4REFBQ3hCLHNLQUFJQTtvREFBQzJDLFdBQVU7Ozs7OzswREFDdkI7Ozs7OzswREFHRCw4REFBQy9CLG1FQUFZQTtnREFDWHdDLFNBQVE7Z0RBQ1JULFdBQVU7Z0RBQ1ZuQixvQkFBTSw4REFBQ3ZCLHNLQUFPQTtvREFBQzBDLFdBQVU7Ozs7OzswREFDMUI7Ozs7OzswREFHRCw4REFBQy9CLG1FQUFZQTtnREFDWHdDLFNBQVE7Z0RBQ1JULFdBQVU7Z0RBQ1ZuQixvQkFBTSw4REFBQ2xCLHVLQUFTQTtvREFBQ3FDLFdBQVU7Ozs7OzswREFDNUI7Ozs7Ozs7Ozs7OztrREFLSCw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNqQyx1S0FBTUE7d0RBQUNpQyxXQUFVOzs7Ozs7a0VBQ2xCLDhEQUFDaUI7d0RBQUtqQixXQUFVO2tFQUFvQzs7Ozs7Ozs7Ozs7OzBEQUV0RCw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQTBCOzs7Ozs7MERBQ3pDLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ0Q7b0RBQUlDLFdBQVU7b0RBQWdFb0IsT0FBTzt3REFBRUMsT0FBTztvREFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU3ZIIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9kYXNoYm9hcmQvcGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnXG5pbXBvcnQge1xuICBQZW5Ub29sLFxuICBNYWlsLFxuICBUd2l0dGVyLFxuICBWaWRlbyxcbiAgVHJlbmRpbmdVcCxcbiAgQ2xvY2ssXG4gIEZpbGVUZXh0LFxuICBaYXAsXG4gIFVzZXJzLFxuICBCYXJDaGFydDMsXG4gIEFycm93UmlnaHQsXG4gIFBsdXMsXG4gIFN0YXIsXG4gIENhbGVuZGFyLFxuICBUYXJnZXRcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IE1vZGVybkNhcmQgZnJvbSAnQC9jb21wb25lbnRzL3VpL01vZGVybkNhcmQnXG5pbXBvcnQgTW9kZXJuQnV0dG9uIGZyb20gJ0AvY29tcG9uZW50cy91aS9Nb2Rlcm5CdXR0b24nXG5pbXBvcnQgRGFzaGJvYXJkTGF5b3V0IGZyb20gJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvRGFzaGJvYXJkTGF5b3V0J1xuaW1wb3J0IHtcbiAgQ29udGVudFZpZXdzV2lkZ2V0LFxuICBFbmdhZ2VtZW50V2lkZ2V0LFxuICBDb250ZW50Q3JlYXRlZFdpZGdldCxcbiAgQXZnUmVhZFRpbWVXaWRnZXQsXG4gIENvbnZlcnNpb25SYXRlV2lkZ2V0LFxuICBBSUdlbmVyYXRpb25zV2lkZ2V0XG59IGZyb20gJ0AvY29tcG9uZW50cy9kYXNoYm9hcmQvQW5hbHl0aWNzV2lkZ2V0J1xuXG5jb25zdCB0b29scyA9IFtcbiAge1xuICAgIGlkOiAnYmxvZycsXG4gICAgdGl0bGU6ICdCbG9nIEdlbmVyYXRvcicsXG4gICAgZGVzY3JpcHRpb246ICdDcmVhdGUgU0VPLW9wdGltaXplZCBibG9nIHBvc3RzIHdpdGggQUkgcmVzZWFyY2gnLFxuICAgIGljb246IFBlblRvb2wsXG4gICAgaHJlZjogJy9ibG9nJyxcbiAgICBncmFkaWVudDogJ2Zyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCcsXG4gICAgdXNhZ2U6ICcyNCBwb3N0cyB0aGlzIG1vbnRoJyxcbiAgICBwb3B1bGFyOiB0cnVlXG4gIH0sXG4gIHtcbiAgICBpZDogJ2VtYWlsJyxcbiAgICB0aXRsZTogJ0VtYWlsIEdlbmVyYXRvcicsXG4gICAgZGVzY3JpcHRpb246ICdDcmFmdCBoaWdoLWNvbnZlcnRpbmcgZW1haWwgY2FtcGFpZ25zJyxcbiAgICBpY29uOiBNYWlsLFxuICAgIGhyZWY6ICcvZW1haWwnLFxuICAgIGdyYWRpZW50OiAnZnJvbS1waW5rLTUwMCB0by1yb3NlLTUwMCcsXG4gICAgdXNhZ2U6ICcxMiBlbWFpbHMgdGhpcyBtb250aCcsXG4gICAgcG9wdWxhcjogZmFsc2VcbiAgfSxcbiAge1xuICAgIGlkOiAndHdlZXQnLFxuICAgIHRpdGxlOiAnVHdlZXQgR2VuZXJhdG9yJyxcbiAgICBkZXNjcmlwdGlvbjogJ0dlbmVyYXRlIHZpcmFsLXdvcnRoeSBzb2NpYWwgbWVkaWEgY29udGVudCcsXG4gICAgaWNvbjogVHdpdHRlcixcbiAgICBocmVmOiAnL3R3ZWV0JyxcbiAgICBncmFkaWVudDogJ2Zyb20tY3lhbi01MDAgdG8tYmx1ZS01MDAnLFxuICAgIHVzYWdlOiAnNDggdHdlZXRzIHRoaXMgbW9udGgnLFxuICAgIHBvcHVsYXI6IGZhbHNlXG4gIH0sXG4gIHtcbiAgICBpZDogJ3lvdXR1YmUnLFxuICAgIHRpdGxlOiAnWW91VHViZSBTY3JpcHRzJyxcbiAgICBkZXNjcmlwdGlvbjogJ0NyZWF0ZSBlbmdhZ2luZyB2aWRlbyBzY3JpcHRzIHdpdGggaG9va3MnLFxuICAgIGljb246IFZpZGVvLFxuICAgIGhyZWY6ICcveW91dHViZScsXG4gICAgZ3JhZGllbnQ6ICdmcm9tLW9yYW5nZS01MDAgdG8tcmVkLTUwMCcsXG4gICAgdXNhZ2U6ICc4IHNjcmlwdHMgdGhpcyBtb250aCcsXG4gICAgcG9wdWxhcjogZmFsc2VcbiAgfVxuXVxuXG5jb25zdCBzdGF0cyA9IFtcbiAge1xuICAgIHRpdGxlOiAnQ29udGVudCBHZW5lcmF0ZWQnLFxuICAgIHZhbHVlOiAnOTInLFxuICAgIGNoYW5nZTogJysxMiUnLFxuICAgIHRyZW5kOiAndXAnLFxuICAgIGljb246IEZpbGVUZXh0LFxuICAgIGNvbG9yOiAnYmx1ZSdcbiAgfSxcbiAge1xuICAgIHRpdGxlOiAnV29yZHMgV3JpdHRlbicsXG4gICAgdmFsdWU6ICc0NS4ySycsXG4gICAgY2hhbmdlOiAnKzglJyxcbiAgICB0cmVuZDogJ3VwJyxcbiAgICBpY29uOiBQZW5Ub29sLFxuICAgIGNvbG9yOiAncHVycGxlJ1xuICB9LFxuICB7XG4gICAgdGl0bGU6ICdUaW1lIFNhdmVkJyxcbiAgICB2YWx1ZTogJzI0aCcsXG4gICAgY2hhbmdlOiAnKzE1JScsXG4gICAgdHJlbmQ6ICd1cCcsXG4gICAgaWNvbjogQ2xvY2ssXG4gICAgY29sb3I6ICdncmVlbidcbiAgfSxcbiAge1xuICAgIHRpdGxlOiAnRW5nYWdlbWVudCBSYXRlJyxcbiAgICB2YWx1ZTogJzk0JScsXG4gICAgY2hhbmdlOiAnKzMlJyxcbiAgICB0cmVuZDogJ3VwJyxcbiAgICBpY29uOiBUcmVuZGluZ1VwLFxuICAgIGNvbG9yOiAnb3JhbmdlJ1xuICB9XG5dXG5cbmNvbnN0IHJlY2VudENvbnRlbnQgPSBbXG4gIHtcbiAgICBpZDogMSxcbiAgICB0aXRsZTogJ1RoZSBGdXR1cmUgb2YgQUkgaW4gSGVhbHRoY2FyZScsXG4gICAgdHlwZTogJ0Jsb2cgUG9zdCcsXG4gICAgY3JlYXRlZEF0OiAnMiBob3VycyBhZ28nLFxuICAgIHN0YXR1czogJ1B1Ymxpc2hlZCcsXG4gICAgZW5nYWdlbWVudDogJzEuMksgdmlld3MnXG4gIH0sXG4gIHtcbiAgICBpZDogMixcbiAgICB0aXRsZTogJ1dlbGNvbWUgdG8gT3VyIE5ld3NsZXR0ZXInLFxuICAgIHR5cGU6ICdFbWFpbCcsXG4gICAgY3JlYXRlZEF0OiAnNSBob3VycyBhZ28nLFxuICAgIHN0YXR1czogJ1NlbnQnLFxuICAgIGVuZ2FnZW1lbnQ6ICc4OSUgb3BlbiByYXRlJ1xuICB9LFxuICB7XG4gICAgaWQ6IDMsXG4gICAgdGl0bGU6ICdUb3AgMTAgQUkgVG9vbHMgZm9yIDIwMjQnLFxuICAgIHR5cGU6ICdUd2VldCBUaHJlYWQnLFxuICAgIGNyZWF0ZWRBdDogJzEgZGF5IGFnbycsXG4gICAgc3RhdHVzOiAnUHVibGlzaGVkJyxcbiAgICBlbmdhZ2VtZW50OiAnMjQ1IGxpa2VzJ1xuICB9XG5dXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZFBhZ2UoKSB7XG4gIGNvbnN0IFttb3VudGVkLCBzZXRNb3VudGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0TW91bnRlZCh0cnVlKVxuICB9LCBbXSlcblxuICBpZiAoIW1vdW50ZWQpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8RGFzaGJvYXJkTGF5b3V0PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cbiAgICAgICAgey8qIFdlbGNvbWUgU2VjdGlvbiAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41IH19XG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LXdoaXRlIG1iLTJcIj5cbiAgICAgICAgICAgICAgICBXZWxjb21lIGJhY2ssIEpvaG4hIPCfkYtcbiAgICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC13aGl0ZS83MFwiPlxuICAgICAgICAgICAgICAgIFJlYWR5IHRvIGNyZWF0ZSBhbWF6aW5nIGNvbnRlbnQ/IExldCdzIHNlZSB3aGF0IHlvdSBjYW4gYnVpbGQgdG9kYXkuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPE1vZGVybkJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwicHJpbWFyeVwiXG4gICAgICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgICAgIGljb249ezxQbHVzIGNsYXNzTmFtZT1cInctNSBoLTVcIiAvPn1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgQ3JlYXRlIENvbnRlbnRcbiAgICAgICAgICAgIDwvTW9kZXJuQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIEFuYWx5dGljcyBHcmlkICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGRlbGF5OiAwLjEgfX1cbiAgICAgICAgICBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02XCJcbiAgICAgICAgPlxuICAgICAgICAgIDxDb250ZW50Q3JlYXRlZFdpZGdldCAvPlxuICAgICAgICAgIDxBdmdSZWFkVGltZVdpZGdldCAvPlxuICAgICAgICAgIDxDb252ZXJzaW9uUmF0ZVdpZGdldCAvPlxuICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgey8qIEFkdmFuY2VkIEFuYWx5dGljcyAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBkZWxheTogMC4xNSB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTIgZ2FwLTZcIlxuICAgICAgICA+XG4gICAgICAgICAgPENvbnRlbnRWaWV3c1dpZGdldCAvPlxuICAgICAgICAgIDxFbmdhZ2VtZW50V2lkZ2V0IC8+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICB7LyogQUkgVXNhZ2UgQW5hbHl0aWNzICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICBhbmltYXRlPXt7IG9wYWNpdHk6IDEsIHk6IDAgfX1cbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGRlbGF5OiAwLjIgfX1cbiAgICAgICAgPlxuICAgICAgICAgIDxBSUdlbmVyYXRpb25zV2lkZ2V0IC8+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICB7LyogVG9vbHMgR3JpZCAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBkZWxheTogMC4yIH19XG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi02XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtd2hpdGVcIj5Db250ZW50IFRvb2xzPC9oMj5cbiAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvdG9vbHNcIiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwIGhvdmVyOnRleHQtYmx1ZS0zMDAgdGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICBWaWV3IGFsbCB0b29scyDihpJcbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICAgICAge3Rvb2xzLm1hcCgodG9vbCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgICBrZXk9e3Rvb2wuaWR9XG4gICAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb249e3sgZHVyYXRpb246IDAuNSwgZGVsYXk6IDAuMyArIGluZGV4ICogMC4xIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8TW9kZXJuQ2FyZCB2YXJpYW50PVwiZWxldmF0ZWRcIiBjbGFzc05hbWU9XCJwLTYgaC1mdWxsIGdyb3VwIGN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgICAgICA8TGluayBocmVmPXt0b29sLmhyZWZ9IGNsYXNzTmFtZT1cImJsb2NrIGgtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BwLTMgcm91bmRlZC14bCBiZy1ncmFkaWVudC10by1yICR7dG9vbC5ncmFkaWVudH1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRvb2wuaWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICB7dG9vbC5wb3B1bGFyICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMSBweC0yIHB5LTEgYmcteWVsbG93LTUwMC8yMCB0ZXh0LXllbGxvdy00MDAgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U3RhciBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBQb3B1bGFyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0yXCI+e3Rvb2wudGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzcwIHRleHQtc20gbWItNCBmbGV4LTFcIj57dG9vbC5kZXNjcmlwdGlvbn08L3A+XG5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC13aGl0ZS81MCB0ZXh0LXhzXCI+e3Rvb2wudXNhZ2V9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlLzUwIGdyb3VwLWhvdmVyOnRleHQtd2hpdGUgZ3JvdXAtaG92ZXI6dHJhbnNsYXRlLXgtMSB0cmFuc2l0aW9uLWFsbFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDwvTW9kZXJuQ2FyZD5cbiAgICAgICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cblxuICAgICAgICB7LyogUmVjZW50IENvbnRlbnQgJiBRdWljayBBY3Rpb25zICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICB7LyogUmVjZW50IENvbnRlbnQgKi99XG4gICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCwgeTogMjAgfX1cbiAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgeTogMCB9fVxuICAgICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41LCBkZWxheTogMC40IH19XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0yXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8TW9kZXJuQ2FyZCB2YXJpYW50PVwiZ2xhc3NcIiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPlJlY2VudCBDb250ZW50PC9oMz5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2xpYnJhcnlcIiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNDAwIGhvdmVyOnRleHQtYmx1ZS0zMDAgdGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgVmlldyBhbGwg4oaSXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIHtyZWNlbnRDb250ZW50Lm1hcCgoY29udGVudCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBrZXk9e2NvbnRlbnQuaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwLTQgcm91bmRlZC14bCBiZy13aGl0ZS81IGhvdmVyOmJnLXdoaXRlLzEwIHRyYW5zaXRpb24tY29sb3JzIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC13aGl0ZSBmb250LW1lZGl1bVwiPntjb250ZW50LnRpdGxlfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCBtdC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwIHRleHQtc21cIj57Y29udGVudC50eXBlfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtd2hpdGUvNTAgdGV4dC1zbVwiPntjb250ZW50LmNyZWF0ZWRBdH08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNDAwIHRleHQtc20gZm9udC1tZWRpdW1cIj57Y29udGVudC5zdGF0dXN9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlLzYwIHRleHQtc21cIj57Y29udGVudC5lbmdhZ2VtZW50fTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvTW9kZXJuQ2FyZD5cbiAgICAgICAgICA8L21vdGlvbi5kaXY+XG5cbiAgICAgICAgICB7LyogUXVpY2sgQWN0aW9ucyAqL31cbiAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxuICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjUsIGRlbGF5OiAwLjUgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8TW9kZXJuQ2FyZCB2YXJpYW50PVwiZ2xhc3NcIiBjbGFzc05hbWU9XCJwLTZcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTZcIj5RdWljayBBY3Rpb25zPC9oMz5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICAgIDxNb2Rlcm5CdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBqdXN0aWZ5LXN0YXJ0XCJcbiAgICAgICAgICAgICAgICAgIGljb249ezxQZW5Ub29sIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPn1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBXcml0ZSBCbG9nIFBvc3RcbiAgICAgICAgICAgICAgICA8L01vZGVybkJ1dHRvbj5cbiAgICAgICAgICAgICAgICA8TW9kZXJuQnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwic2Vjb25kYXJ5XCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBqdXN0aWZ5LXN0YXJ0XCJcbiAgICAgICAgICAgICAgICAgIGljb249ezxNYWlsIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPn1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDcmVhdGUgRW1haWxcbiAgICAgICAgICAgICAgICA8L01vZGVybkJ1dHRvbj5cbiAgICAgICAgICAgICAgICA8TW9kZXJuQnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwic2Vjb25kYXJ5XCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBqdXN0aWZ5LXN0YXJ0XCJcbiAgICAgICAgICAgICAgICAgIGljb249ezxUd2l0dGVyIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPn1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBHZW5lcmF0ZSBUd2VldFxuICAgICAgICAgICAgICAgIDwvTW9kZXJuQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxNb2Rlcm5CdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzZWNvbmRhcnlcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGp1c3RpZnktc3RhcnRcIlxuICAgICAgICAgICAgICAgICAgaWNvbj17PEJhckNoYXJ0MyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz59XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgVmlldyBBbmFseXRpY3NcbiAgICAgICAgICAgICAgICA8L01vZGVybkJ1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IHAtNCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAvMjAgdG8tcHVycGxlLTYwMC8yMCByb3VuZGVkLXhsIGJvcmRlciBib3JkZXItYmx1ZS01MDAvMzBcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxUYXJnZXQgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS00MDAgZm9udC1tZWRpdW0gdGV4dC1zbVwiPk1vbnRobHkgR29hbDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgdGV4dC1zbSBtYi0yXCI+Q29udGVudCBwaWVjZXM6IDkyLzEwMDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGJnLXdoaXRlLzEwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLXB1cnBsZS02MDAgaC0yIHJvdW5kZWQtZnVsbFwiIHN0eWxlPXt7IHdpZHRoOiAnOTIlJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L01vZGVybkNhcmQ+XG4gICAgICAgICAgPC9tb3Rpb24uZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvRGFzaGJvYXJkTGF5b3V0PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJtb3Rpb24iLCJMaW5rIiwiUGVuVG9vbCIsIk1haWwiLCJUd2l0dGVyIiwiVmlkZW8iLCJUcmVuZGluZ1VwIiwiQ2xvY2siLCJGaWxlVGV4dCIsIkJhckNoYXJ0MyIsIkFycm93UmlnaHQiLCJQbHVzIiwiU3RhciIsIlRhcmdldCIsIk1vZGVybkNhcmQiLCJNb2Rlcm5CdXR0b24iLCJEYXNoYm9hcmRMYXlvdXQiLCJDb250ZW50Vmlld3NXaWRnZXQiLCJFbmdhZ2VtZW50V2lkZ2V0IiwiQ29udGVudENyZWF0ZWRXaWRnZXQiLCJBdmdSZWFkVGltZVdpZGdldCIsIkNvbnZlcnNpb25SYXRlV2lkZ2V0IiwiQUlHZW5lcmF0aW9uc1dpZGdldCIsInRvb2xzIiwiaWQiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiaWNvbiIsImhyZWYiLCJncmFkaWVudCIsInVzYWdlIiwicG9wdWxhciIsInN0YXRzIiwidmFsdWUiLCJjaGFuZ2UiLCJ0cmVuZCIsImNvbG9yIiwicmVjZW50Q29udGVudCIsInR5cGUiLCJjcmVhdGVkQXQiLCJzdGF0dXMiLCJlbmdhZ2VtZW50IiwiRGFzaGJvYXJkUGFnZSIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwiZGl2IiwiY2xhc3NOYW1lIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJ5IiwiYW5pbWF0ZSIsInRyYW5zaXRpb24iLCJkdXJhdGlvbiIsImgxIiwicCIsInZhcmlhbnQiLCJzaXplIiwiZGVsYXkiLCJoMiIsIm1hcCIsInRvb2wiLCJpbmRleCIsImgzIiwic3BhbiIsImNvbnRlbnQiLCJoNCIsInN0eWxlIiwid2lkdGgiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/AnalyticsWidget.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/AnalyticsWidget.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIGenerationsWidget: () => (/* binding */ AIGenerationsWidget),\n/* harmony export */   AvgReadTimeWidget: () => (/* binding */ AvgReadTimeWidget),\n/* harmony export */   ContentCreatedWidget: () => (/* binding */ ContentCreatedWidget),\n/* harmony export */   ContentViewsWidget: () => (/* binding */ ContentViewsWidget),\n/* harmony export */   ConversionRateWidget: () => (/* binding */ ConversionRateWidget),\n/* harmony export */   EngagementWidget: () => (/* binding */ EngagementWidget),\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsWidget)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Eye,Target,TrendingDown,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Eye,Target,TrendingDown,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Eye,Target,TrendingDown,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Eye,Target,TrendingDown,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Eye,Target,TrendingDown,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Eye,Target,TrendingDown,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Eye,Target,TrendingDown,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Clock,Eye,Target,TrendingDown,TrendingUp,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _ui_ModernCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ui/ModernCard */ \"(ssr)/./src/components/ui/ModernCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,ContentViewsWidget,EngagementWidget,ContentCreatedWidget,AvgReadTimeWidget,ConversionRateWidget,AIGenerationsWidget auto */ \n\n\n\n\nconst mockData = [\n    {\n        name: 'Jan',\n        value: 400,\n        previous: 350\n    },\n    {\n        name: 'Feb',\n        value: 300,\n        previous: 280\n    },\n    {\n        name: 'Mar',\n        value: 600,\n        previous: 450\n    },\n    {\n        name: 'Apr',\n        value: 800,\n        previous: 600\n    },\n    {\n        name: 'May',\n        value: 700,\n        previous: 650\n    },\n    {\n        name: 'Jun',\n        value: 900,\n        previous: 750\n    },\n    {\n        name: 'Jul',\n        value: 1100,\n        previous: 900\n    }\n];\nfunction AnalyticsWidget({ title, data, type, icon, color = '#3b82f6', showTrend = true, className = '' }) {\n    const currentValue = data[data.length - 1]?.value || 0;\n    const previousValue = data[data.length - 2]?.value || 0;\n    const changePercent = previousValue > 0 ? (currentValue - previousValue) / previousValue * 100 : 0;\n    const isPositive = changePercent >= 0;\n    const CustomTooltip = ({ active, payload, label })=>{\n        if (active && payload && payload.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl p-3 shadow-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/60 text-sm\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white font-semibold\",\n                        children: `${payload[0].value.toLocaleString()}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    const renderChart = ()=>{\n        switch(type){\n            case 'line':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 120,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.LineChart, {\n                        data: mockData,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                stroke: \"rgba(255,255,255,0.1)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {\n                                dataKey: \"name\",\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fill: 'rgba(255,255,255,0.6)',\n                                    fontSize: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {\n                                hide: true\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 33\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.Line, {\n                                type: \"monotone\",\n                                dataKey: \"value\",\n                                stroke: color,\n                                strokeWidth: 3,\n                                dot: {\n                                    fill: color,\n                                    strokeWidth: 2,\n                                    r: 4\n                                },\n                                activeDot: {\n                                    r: 6,\n                                    stroke: color,\n                                    strokeWidth: 2\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 11\n                }, this);\n            case 'area':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                    width: \"100%\",\n                    height: 120,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.AreaChart, {\n                        data: mockData,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.CartesianGrid, {\n                                strokeDasharray: \"3 3\",\n                                stroke: \"rgba(255,255,255,0.1)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.XAxis, {\n                                dataKey: \"name\",\n                                axisLine: false,\n                                tickLine: false,\n                                tick: {\n                                    fill: 'rgba(255,255,255,0.6)',\n                                    fontSize: 12\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {\n                                hide: true\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 33\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                    id: `gradient-${title}`,\n                                    x1: \"0\",\n                                    y1: \"0\",\n                                    x2: \"0\",\n                                    y2: \"1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"5%\",\n                                            stopColor: color,\n                                            stopOpacity: 0.3\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                            offset: \"95%\",\n                                            stopColor: color,\n                                            stopOpacity: 0\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Area, {\n                                type: \"monotone\",\n                                dataKey: \"value\",\n                                stroke: color,\n                                strokeWidth: 2,\n                                fillOpacity: 1,\n                                fill: `url(#gradient-${title})`\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    if (type === 'stat') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModernCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            variant: \"glass\",\n            className: `p-6 ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.5\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 rounded-xl bg-white/10\",\n                                        children: icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white/80 font-medium\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            showTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex items-center gap-1 ${isPositive ? 'text-green-400' : 'text-red-400'}`,\n                                children: [\n                                    isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium\",\n                                        children: [\n                                            Math.abs(changePercent).toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold text-white\",\n                                children: currentValue.toLocaleString()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-white/60 text-sm\",\n                                children: [\n                                    \"vs \",\n                                    previousValue.toLocaleString(),\n                                    \" last period\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                lineNumber: 137,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModernCard__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        variant: \"glass\",\n        className: `p-6 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 rounded-xl bg-white/10\",\n                                children: icon\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-white font-semibold\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mt-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-white\",\n                                                children: currentValue.toLocaleString()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this),\n                                            showTrend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex items-center gap-1 ${isPositive ? 'text-green-400' : 'text-red-400'}`,\n                                                children: [\n                                                    isPositive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 23\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: [\n                                                            Math.abs(changePercent).toFixed(1),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4\",\n                    children: renderChart()\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n            lineNumber: 180,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, this);\n}\n// Pre-configured analytics widgets\nconst ContentViewsWidget = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsWidget, {\n        title: \"Content Views\",\n        data: mockData,\n        type: \"area\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"w-5 h-5 text-blue-400\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n            lineNumber: 231,\n            columnNumber: 11\n        }, void 0),\n        color: \"#3b82f6\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n        lineNumber: 227,\n        columnNumber: 3\n    }, undefined);\nconst EngagementWidget = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsWidget, {\n        title: \"Engagement Rate\",\n        data: mockData,\n        type: \"line\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"w-5 h-5 text-purple-400\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n            lineNumber: 241,\n            columnNumber: 11\n        }, void 0),\n        color: \"#8b5cf6\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n        lineNumber: 237,\n        columnNumber: 3\n    }, undefined);\nconst ContentCreatedWidget = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsWidget, {\n        title: \"Content Created\",\n        data: [\n            {\n                name: 'Total',\n                value: 47\n            },\n            {\n                name: 'Previous',\n                value: 32\n            }\n        ],\n        type: \"stat\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            className: \"w-5 h-5 text-green-400\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n            lineNumber: 251,\n            columnNumber: 11\n        }, void 0)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n        lineNumber: 247,\n        columnNumber: 3\n    }, undefined);\nconst AvgReadTimeWidget = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsWidget, {\n        title: \"Avg. Read Time\",\n        data: [\n            {\n                name: 'Current',\n                value: 3.2\n            },\n            {\n                name: 'Previous',\n                value: 2.8\n            }\n        ],\n        type: \"stat\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            className: \"w-5 h-5 text-orange-400\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n            lineNumber: 260,\n            columnNumber: 11\n        }, void 0)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n        lineNumber: 256,\n        columnNumber: 3\n    }, undefined);\nconst ConversionRateWidget = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsWidget, {\n        title: \"Conversion Rate\",\n        data: [\n            {\n                name: 'Current',\n                value: 12.5\n            },\n            {\n                name: 'Previous',\n                value: 9.8\n            }\n        ],\n        type: \"stat\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            className: \"w-5 h-5 text-pink-400\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n            lineNumber: 269,\n            columnNumber: 11\n        }, void 0)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n        lineNumber: 265,\n        columnNumber: 3\n    }, undefined);\nconst AIGenerationsWidget = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsWidget, {\n        title: \"AI Generations\",\n        data: mockData,\n        type: \"area\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Clock_Eye_Target_TrendingDown_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            className: \"w-5 h-5 text-yellow-400\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n            lineNumber: 278,\n            columnNumber: 11\n        }, void 0),\n        color: \"#f59e0b\"\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/AnalyticsWidget.tsx\",\n        lineNumber: 274,\n        columnNumber: 3\n    }, undefined);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/AnalyticsWidget.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/DashboardLayout.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/DashboardLayout.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/dashboard/Sidebar.tsx\");\n/* harmony import */ var _TopBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TopBar */ \"(ssr)/./src/components/dashboard/TopBar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction DashboardLayout({ children }) {\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            setMounted(true);\n            // Check for saved sidebar state\n            const saved = localStorage.getItem('sidebar-collapsed');\n            if (saved) {\n                setSidebarCollapsed(JSON.parse(saved));\n            }\n        }\n    }[\"DashboardLayout.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            if (mounted) {\n                localStorage.setItem('sidebar-collapsed', JSON.stringify(sidebarCollapsed));\n            }\n        }\n    }[\"DashboardLayout.useEffect\"], [\n        sidebarCollapsed,\n        mounted\n    ]);\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    if (!mounted) {\n        return null // Prevent hydration mismatch\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isCollapsed: sidebarCollapsed,\n                onToggle: toggleSidebar\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/DashboardLayout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                animate: {\n                    marginLeft: sidebarCollapsed ? 80 : 280\n                },\n                transition: {\n                    duration: 0.3,\n                    ease: \"easeInOut\"\n                },\n                className: \"min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TopBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/DashboardLayout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.5\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/DashboardLayout.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/DashboardLayout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/DashboardLayout.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden -z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/DashboardLayout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: '2s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/DashboardLayout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-3/4 left-1/3 w-64 h-64 bg-cyan-500/5 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: '4s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/DashboardLayout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/DashboardLayout.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/DashboardLayout.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./src/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/SettingsContext */ \"(ssr)/./src/contexts/SettingsContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-tool.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,FileText,HelpCircle,LayoutDashboard,LogOut,Mail,PenTool,Settings,Sparkles,Twitter,User,Video!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst navigationItems = [\n    {\n        title: 'Dashboard',\n        href: '/dashboard',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        badge: null\n    },\n    {\n        title: 'KaibanJS Super Agent',\n        href: '/kaiban-superagent',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        badge: 'New'\n    },\n    {\n        title: 'Multi-Agent System',\n        href: '/multi-agent-superagent',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        badge: 'Enhanced'\n    },\n    {\n        title: 'AI Superagent',\n        href: '/superagent',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        badge: 'Legacy'\n    },\n    {\n        title: 'Content Tools',\n        items: [\n            {\n                title: 'Blog Generator',\n                href: '/blog',\n                icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                badge: 'Popular'\n            },\n            {\n                title: 'Email Generator',\n                href: '/email',\n                icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                badge: null\n            },\n            {\n                title: 'Tweet Generator',\n                href: '/tweet',\n                icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                badge: 'New'\n            },\n            {\n                title: 'YouTube Scripts',\n                href: '/youtube',\n                icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                badge: null\n            }\n        ]\n    },\n    {\n        title: 'Analytics',\n        href: '/analytics',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        badge: null\n    },\n    {\n        title: 'Content Library',\n        href: '/library',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        badge: null\n    }\n];\nconst bottomItems = [\n    {\n        title: 'Settings',\n        href: '/settings',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        title: 'Help & Support',\n        href: '/help',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nfunction Sidebar({ isCollapsed, onToggle }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { settings } = (0,_contexts_SettingsContext__WEBPACK_IMPORTED_MODULE_4__.useSettings)();\n    const isActive = (href)=>{\n        if (href === '/dashboard') {\n            return pathname === '/dashboard';\n        }\n        return pathname.startsWith(href);\n    };\n    const SidebarItem = ({ item, isNested = false })=>{\n        const active = isActive(item.href);\n        const Icon = item.icon;\n        const isItemHovered = hoveredItem === item.href;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: item.href,\n                    className: `\n            relative flex items-center gap-3 px-3 py-2.5 rounded-xl transition-all duration-200 cursor-pointer\n            ${active ? 'bg-gradient-to-r from-blue-500/20 to-purple-600/20 text-white border border-blue-500/30' : 'text-white/70 hover:text-white hover:bg-white/5'}\n            ${isNested ? 'ml-6' : ''}\n            ${isCollapsed ? 'justify-center' : ''}\n          `,\n                    onMouseEnter: ()=>setHoveredItem(item.href),\n                    onMouseLeave: ()=>setHoveredItem(null),\n                    children: [\n                        active && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-600/10 rounded-xl\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 flex items-center gap-3 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                    className: `w-5 h-5 flex-shrink-0 ${active ? 'text-blue-400' : ''}`\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `\n                    px-2 py-0.5 text-xs font-semibold rounded-full\n                    ${item.badge === 'New' ? 'bg-green-500/20 text-green-400 border border-green-500/30' : 'bg-orange-500/20 text-orange-400 border border-orange-500/30'}\n                  `,\n                                            children: item.badge\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this),\n                isCollapsed && isItemHovered && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-full ml-4 px-3 py-2 bg-gray-800/95 backdrop-blur-sm text-white text-sm rounded-lg shadow-xl border border-white/20 whitespace-nowrap z-[60]\",\n                    style: {\n                        top: '50%',\n                        transform: 'translateY(-50%)',\n                        pointerEvents: 'none'\n                    },\n                    children: [\n                        item.title,\n                        item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-1.5 py-0.5 text-xs bg-blue-500/20 text-blue-400 rounded\",\n                            children: item.badge\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.aside, {\n        animate: {\n            width: isCollapsed ? 80 : 280\n        },\n        transition: {\n            duration: 0.3,\n            ease: \"easeInOut\"\n        },\n        className: \"fixed left-0 top-0 h-full bg-gray-900/95 backdrop-blur-xl border-r border-white/10 z-40 flex flex-col\",\n        onMouseLeave: ()=>setHoveredItem(null),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-white/10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-8 h-8 text-blue-400\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"Invincible\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onToggle,\n                            className: \"p-2 rounded-lg hover:bg-white/10 transition-colors duration-200 cursor-pointer\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-5 h-5 text-white/70\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"w-5 h-5 text-white/70\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-4 space-y-2 overflow-y-auto\",\n                children: navigationItems.map((section, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: section.items ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2 text-xs font-semibold text-white/50 uppercase tracking-wider\",\n                                    children: section.title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 19\n                                }, this),\n                                section.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                        item: item,\n                                        isNested: !isCollapsed\n                                    }, item.href, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 19\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                            item: section\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 15\n                        }, this)\n                    }, index, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-white/10 space-y-2\",\n                children: [\n                    bottomItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                            item: item\n                        }, item.href, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 11\n                        }, this)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n          flex items-center gap-3 p-3 rounded-xl bg-white/5 border border-white/10\n          ${isCollapsed ? 'justify-center' : ''}\n        `,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"w-4 h-4 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-white\",\n                                                children: [\n                                                    settings.firstName,\n                                                    \" \",\n                                                    settings.lastName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-white/60\",\n                                                children: \"Pro Plan\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"p-1 rounded-lg hover:bg-white/10 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_FileText_HelpCircle_LayoutDashboard_LogOut_Mail_PenTool_Settings_Sparkles_Twitter_User_Video_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"w-4 h-4 text-white/70\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/Sidebar.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/TopBar.tsx":
/*!*********************************************!*\
  !*** ./src/components/dashboard/TopBar.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TopBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Crown,LogOut,Plus,Search,Settings,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Crown,LogOut,Plus,Search,Settings,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Crown,LogOut,Plus,Search,Settings,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Crown,LogOut,Plus,Search,Settings,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Crown,LogOut,Plus,Search,Settings,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Crown,LogOut,Plus,Search,Settings,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Crown,LogOut,Plus,Search,Settings,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Crown,LogOut,Plus,Search,Settings,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Crown,LogOut,Plus,Search,Settings,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,ChevronDown,CreditCard,Crown,LogOut,Plus,Search,Settings,User,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_ModernButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ModernButton */ \"(ssr)/./src/components/ui/ModernButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction TopBar() {\n    const [searchFocused, setSearchFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [profileOpen, setProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [notificationsOpen, setNotificationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const notifications = [\n        {\n            id: 1,\n            title: 'Blog post generated',\n            message: 'Your \"AI in Healthcare\" blog post is ready',\n            time: '2 min ago',\n            unread: true\n        },\n        {\n            id: 2,\n            title: 'Usage limit warning',\n            message: 'You\\'ve used 80% of your monthly credits',\n            time: '1 hour ago',\n            unread: true\n        },\n        {\n            id: 3,\n            title: 'New feature available',\n            message: 'Try our new YouTube script generator',\n            time: '1 day ago',\n            unread: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-30 bg-white/5 backdrop-blur-xl border-b border-white/10\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between px-6 py-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 max-w-xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: `\n              relative flex items-center transition-all duration-300\n              ${searchFocused ? 'scale-105' : ''}\n            `,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"absolute left-3 w-5 h-5 text-white/50\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search content, templates, or ask AI...\",\n                                className: \"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-blue-400 focus:bg-white/15 transition-all duration-300\",\n                                onFocus: ()=>setSearchFocused(true),\n                                onBlur: ()=>setSearchFocused(false)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            searchFocused && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: 10\n                                },\n                                className: \"absolute top-full left-0 right-0 mt-2 bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-white/60 mb-2\",\n                                        children: \"Quick Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 p-2 rounded-lg hover:bg-white/5 cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: \"Create new blog post\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 p-2 rounded-lg hover:bg-white/5 cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4 text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white\",\n                                                        children: \"Generate email campaign\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModernButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"primary\",\n                            size: \"sm\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 19\n                            }, void 0),\n                            children: \"Create\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModernButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"accent\",\n                            size: \"sm\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 19\n                            }, void 0),\n                            className: \"bg-gradient-to-r from-yellow-500 to-orange-500\",\n                            children: \"Upgrade\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                    onClick: ()=>setNotificationsOpen(!notificationsOpen),\n                                    className: \"relative p-2 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        notifications.some((n)=>n.unread) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-gray-900\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                    children: notificationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10,\n                                            scale: 0.95\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0,\n                                            scale: 1\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: 10,\n                                            scale: 0.95\n                                        },\n                                        className: \"absolute top-full right-0 mt-2 w-80 bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl p-4 z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-semibold text-white\",\n                                                        children: \"Notifications\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-sm text-blue-400 hover:text-blue-300\",\n                                                        children: \"Mark all read\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-80 overflow-y-auto\",\n                                                children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `\n                          p-3 rounded-lg border transition-colors cursor-pointer\n                          ${notification.unread ? 'bg-blue-500/10 border-blue-500/20 hover:bg-blue-500/15' : 'bg-white/5 border-white/10 hover:bg-white/10'}\n                        `,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-sm font-medium text-white\",\n                                                                            children: notification.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                            lineNumber: 154,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-white/70 mt-1\",\n                                                                            children: notification.message\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                            lineNumber: 155,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-white/50 mt-2 block\",\n                                                                            children: notification.time\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                            lineNumber: 156,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                    lineNumber: 153,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                notification.unread && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-2 h-2 bg-blue-400 rounded-full mt-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, notification.id, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                    onClick: ()=>setProfileOpen(!profileOpen),\n                                    className: \"flex items-center gap-2 p-2 rounded-xl bg-white/10 hover:bg-white/20 transition-colors\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-4 h-4 text-white/70\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.AnimatePresence, {\n                                    children: profileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10,\n                                            scale: 0.95\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0,\n                                            scale: 1\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: 10,\n                                            scale: 0.95\n                                        },\n                                        className: \"absolute top-full right-0 mt-2 w-64 bg-gray-900/95 backdrop-blur-xl border border-white/10 rounded-xl shadow-2xl p-4 z-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 mb-4 pb-4 border-b border-white/10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-6 h-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: \"John Doe\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-white/60\",\n                                                                children: \"<EMAIL>\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-blue-400 font-medium\",\n                                                                children: \"Pro Plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 196,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4 text-white/70\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-white\",\n                                                                children: \"Profile Settings\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-4 h-4 text-white/70\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-white\",\n                                                                children: \"Billing & Usage\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-white/10 transition-colors text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-4 h-4 text-white/70\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-white\",\n                                                                children: \"Preferences\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"border-white/10 my-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"w-full flex items-center gap-3 p-2 rounded-lg hover:bg-red-500/20 transition-colors text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_ChevronDown_CreditCard_Crown_LogOut_Plus_Search_Settings_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"w-4 h-4 text-red-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-red-400\",\n                                                                children: \"Sign Out\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/dashboard/TopBar.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/TopBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ModernButton.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/ModernButton.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ModernButton({ children, variant = 'primary', size = 'md', disabled = false, loading = false, onClick, type = 'button', className = '', icon }) {\n    const baseClasses = 'btn-modern';\n    const variantClasses = {\n        primary: 'btn-modern-primary',\n        secondary: 'btn-modern-secondary',\n        accent: 'btn-modern-accent'\n    };\n    const sizeClasses = {\n        sm: 'text-sm',\n        md: 'text-base',\n        lg: 'text-lg'\n    };\n    const classes = `\n    ${baseClasses}\n    ${variantClasses[variant]}\n    ${sizeClasses[size]}\n    ${disabled || loading ? 'opacity-50 cursor-not-allowed' : ''}\n    ${className}\n  `.trim();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.button, {\n        type: type,\n        className: classes,\n        onClick: disabled || loading ? undefined : onClick,\n        disabled: disabled || loading,\n        whileHover: disabled || loading ? {} : {\n            scale: 1.02\n        },\n        whileTap: disabled || loading ? {} : {\n            scale: 0.98\n        },\n        transition: {\n            duration: 0.2\n        },\n        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"loading-spinner\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ui/ModernButton.tsx\",\n            lineNumber: 61,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"flex-shrink-0\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ui/ModernButton.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 20\n                }, this),\n                children\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ui/ModernButton.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ModernButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/ModernCard.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/ModernCard.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModernCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ModernCard({ children, variant = 'default', hover = true, className = '', onClick }) {\n    const variantClasses = {\n        default: 'card-modern',\n        elevated: 'card-modern-elevated',\n        glass: 'glass',\n        neu: 'card-neu'\n    };\n    const classes = `\n    ${variantClasses[variant]}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `.trim();\n    const motionProps = hover ? {\n        whileHover: {\n            y: -4,\n            scale: 1.02\n        },\n        transition: {\n            duration: 0.3\n        }\n    } : {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_1__.motion.div, {\n        className: classes,\n        onClick: onClick,\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.5\n        },\n        ...motionProps,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/ui/ModernCard.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/ModernCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/SettingsContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/SettingsContext.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SettingsProvider: () => (/* binding */ SettingsProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useSettings: () => (/* binding */ useSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useSettings,SettingsProvider,default auto */ \n\nconst defaultSettings = {\n    firstName: 'John',\n    lastName: 'Doe',\n    email: '<EMAIL>',\n    bio: 'Content creator and digital marketer passionate about AI-powered writing.',\n    avatar: '',\n    defaultLanguage: 'en',\n    timezone: 'America/New_York',\n    defaultWordCount: 1000,\n    defaultTone: 'professional',\n    includeResearchByDefault: true,\n    autoSaveEnabled: true,\n    emailNotifications: true,\n    pushNotifications: false,\n    weeklyReports: true,\n    marketingEmails: false,\n    theme: 'dark',\n    accentColor: 'blue',\n    animationsEnabled: true,\n    compactMode: false,\n    profileVisibility: 'private',\n    dataSharing: false,\n    analyticsTracking: true\n};\nconst SettingsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useSettings = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SettingsContext);\n    if (context === undefined) {\n        throw new Error('useSettings must be used within a SettingsProvider');\n    }\n    return context;\n};\nconst SettingsProvider = ({ children })=>{\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultSettings);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Load settings from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            try {\n                const savedSettings = localStorage.getItem('userSettings');\n                if (savedSettings) {\n                    const parsed = JSON.parse(savedSettings);\n                    setSettings({\n                        ...defaultSettings,\n                        ...parsed\n                    });\n                }\n            } catch (err) {\n                console.error('Failed to load settings:', err);\n                setError('Failed to load user settings');\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"SettingsProvider.useEffect\"], []);\n    // Save settings to localStorage whenever they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsProvider.useEffect\": ()=>{\n            if (!isLoading) {\n                try {\n                    localStorage.setItem('userSettings', JSON.stringify(settings));\n                } catch (err) {\n                    console.error('Failed to save settings:', err);\n                    setError('Failed to save settings');\n                }\n            }\n        }\n    }[\"SettingsProvider.useEffect\"], [\n        settings,\n        isLoading\n    ]);\n    const updateSettings = (newSettings)=>{\n        setSettings((prev)=>({\n                ...prev,\n                ...newSettings\n            }));\n        setError(null);\n    };\n    const resetSettings = ()=>{\n        setSettings(defaultSettings);\n        setError(null);\n    };\n    const saveSettings = async ()=>{\n        try {\n            setError(null);\n            // Save to backend API\n            const response = await fetch('/api/settings', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(settings)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Failed to save settings');\n            }\n        // Settings are already saved to localStorage via useEffect\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to save settings';\n            setError(errorMessage);\n            throw new Error(errorMessage);\n        }\n    };\n    const value = {\n        settings,\n        updateSettings,\n        resetSettings,\n        saveSettings,\n        isLoading,\n        error\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SettingsContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/SettingsContext.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/SettingsContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _SettingsContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./SettingsContext */ \"(ssr)/./src/contexts/SettingsContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider,default auto */ \n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n};\nconst ThemeProvider = ({ children })=>{\n    const { settings, updateSettings } = (0,_SettingsContext__WEBPACK_IMPORTED_MODULE_2__.useSettings)();\n    // Determine actual theme based on settings and system preference\n    const getActualTheme = ()=>{\n        if (settings.theme === 'auto') {\n            if (false) {}\n            return 'dark' // Default fallback\n            ;\n        }\n        return settings.theme;\n    };\n    const actualTheme = getActualTheme();\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (true) return;\n            const root = document.documentElement;\n            // Remove existing theme classes\n            root.classList.remove('dark', 'light');\n            // Add current theme class\n            root.classList.add(actualTheme);\n            // Apply accent color CSS variables\n            const accentColors = {\n                blue: {\n                    primary: '#2563eb',\n                    secondary: '#1e40af',\n                    light: '#60a5fa'\n                },\n                purple: {\n                    primary: '#7c3aed',\n                    secondary: '#6d28d9',\n                    light: '#a78bfa'\n                },\n                green: {\n                    primary: '#059669',\n                    secondary: '#047857',\n                    light: '#34d399'\n                },\n                red: {\n                    primary: '#e11d48',\n                    secondary: '#be185d',\n                    light: '#fb7185'\n                },\n                orange: {\n                    primary: '#d97706',\n                    secondary: '#b45309',\n                    light: '#fbbf24'\n                }\n            };\n            const colors = accentColors[settings.accentColor] || accentColors.blue;\n            root.style.setProperty('--accent-primary', colors.primary);\n            root.style.setProperty('--accent-secondary', colors.secondary);\n            root.style.setProperty('--accent-light', colors.light);\n            // Apply animations setting\n            if (!settings.animationsEnabled) {\n                root.style.setProperty('--animation-duration', '0s');\n                root.style.setProperty('--transition-duration', '0s');\n            } else {\n                root.style.setProperty('--animation-duration', '0.3s');\n                root.style.setProperty('--transition-duration', '0.2s');\n            }\n            // Apply compact mode\n            if (settings.compactMode) {\n                root.classList.add('compact-mode');\n            } else {\n                root.classList.remove('compact-mode');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        actualTheme,\n        settings.accentColor,\n        settings.animationsEnabled,\n        settings.compactMode\n    ]);\n    // Listen for system theme changes when in auto mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (settings.theme !== 'auto' || \"undefined\" === 'undefined') return;\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    // Force re-render by updating a dummy state or triggering theme application\n                    const root = document.documentElement;\n                    root.classList.remove('dark', 'light');\n                    root.classList.add(getActualTheme());\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        settings.theme\n    ]);\n    const setTheme = (theme)=>{\n        updateSettings({\n            theme\n        });\n    };\n    const setAccentColor = (color)=>{\n        updateSettings({\n            accentColor: color\n        });\n    };\n    const toggleAnimations = ()=>{\n        updateSettings({\n            animationsEnabled: !settings.animationsEnabled\n        });\n    };\n    const toggleCompactMode = ()=>{\n        updateSettings({\n            compactMode: !settings.compactMode\n        });\n    };\n    const value = {\n        theme: actualTheme,\n        accentColor: settings.accentColor,\n        animationsEnabled: settings.animationsEnabled,\n        compactMode: settings.compactMode,\n        setTheme,\n        setAccentColor,\n        toggleAnimations,\n        toggleCompactMode\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/contexts/ThemeContext.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ThemeContext);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvVGhlbWVDb250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEU7QUFDL0I7QUFhL0MsTUFBTUssNkJBQWVKLG9EQUFhQSxDQUErQks7QUFFMUQsTUFBTUMsV0FBVztJQUN0QixNQUFNQyxVQUFVTixpREFBVUEsQ0FBQ0c7SUFDM0IsSUFBSUcsWUFBWUYsV0FBVztRQUN6QixNQUFNLElBQUlHLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNULEVBQUM7QUFNTSxNQUFNRSxnQkFBOEMsQ0FBQyxFQUFFQyxRQUFRLEVBQUU7SUFDdEUsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLGNBQWMsRUFBRSxHQUFHVCw2REFBV0E7SUFFaEQsaUVBQWlFO0lBQ2pFLE1BQU1VLGlCQUFpQjtRQUNyQixJQUFJRixTQUFTRyxLQUFLLEtBQUssUUFBUTtZQUM3QixJQUFJLEtBQTZCLEVBQUUsRUFFbEM7WUFDRCxPQUFPLE9BQU8sbUJBQW1COztRQUNuQztRQUNBLE9BQU9ILFNBQVNHLEtBQUs7SUFDdkI7SUFFQSxNQUFNSSxjQUFjTDtJQUVwQiwwQkFBMEI7SUFDMUJYLGdEQUFTQTttQ0FBQztZQUNSLElBQUksSUFBNkIsRUFBRTtZQUVuQyxNQUFNaUIsT0FBT0MsU0FBU0MsZUFBZTtZQUVyQyxnQ0FBZ0M7WUFDaENGLEtBQUtHLFNBQVMsQ0FBQ0MsTUFBTSxDQUFDLFFBQVE7WUFFOUIsMEJBQTBCO1lBQzFCSixLQUFLRyxTQUFTLENBQUNFLEdBQUcsQ0FBQ047WUFFbkIsbUNBQW1DO1lBQ25DLE1BQU1PLGVBQWU7Z0JBQ25CQyxNQUFNO29CQUNKQyxTQUFTO29CQUNUQyxXQUFXO29CQUNYQyxPQUFPO2dCQUNUO2dCQUNBQyxRQUFRO29CQUNOSCxTQUFTO29CQUNUQyxXQUFXO29CQUNYQyxPQUFPO2dCQUNUO2dCQUNBRSxPQUFPO29CQUNMSixTQUFTO29CQUNUQyxXQUFXO29CQUNYQyxPQUFPO2dCQUNUO2dCQUNBRyxLQUFLO29CQUNITCxTQUFTO29CQUNUQyxXQUFXO29CQUNYQyxPQUFPO2dCQUNUO2dCQUNBSSxRQUFRO29CQUNOTixTQUFTO29CQUNUQyxXQUFXO29CQUNYQyxPQUFPO2dCQUNUO1lBQ0Y7WUFFQSxNQUFNSyxTQUFTVCxZQUFZLENBQUNkLFNBQVN3QixXQUFXLENBQThCLElBQUlWLGFBQWFDLElBQUk7WUFFbkdQLEtBQUtpQixLQUFLLENBQUNDLFdBQVcsQ0FBQyxvQkFBb0JILE9BQU9QLE9BQU87WUFDekRSLEtBQUtpQixLQUFLLENBQUNDLFdBQVcsQ0FBQyxzQkFBc0JILE9BQU9OLFNBQVM7WUFDN0RULEtBQUtpQixLQUFLLENBQUNDLFdBQVcsQ0FBQyxrQkFBa0JILE9BQU9MLEtBQUs7WUFFckQsMkJBQTJCO1lBQzNCLElBQUksQ0FBQ2xCLFNBQVMyQixpQkFBaUIsRUFBRTtnQkFDL0JuQixLQUFLaUIsS0FBSyxDQUFDQyxXQUFXLENBQUMsd0JBQXdCO2dCQUMvQ2xCLEtBQUtpQixLQUFLLENBQUNDLFdBQVcsQ0FBQyx5QkFBeUI7WUFDbEQsT0FBTztnQkFDTGxCLEtBQUtpQixLQUFLLENBQUNDLFdBQVcsQ0FBQyx3QkFBd0I7Z0JBQy9DbEIsS0FBS2lCLEtBQUssQ0FBQ0MsV0FBVyxDQUFDLHlCQUF5QjtZQUNsRDtZQUVBLHFCQUFxQjtZQUNyQixJQUFJMUIsU0FBUzRCLFdBQVcsRUFBRTtnQkFDeEJwQixLQUFLRyxTQUFTLENBQUNFLEdBQUcsQ0FBQztZQUNyQixPQUFPO2dCQUNMTCxLQUFLRyxTQUFTLENBQUNDLE1BQU0sQ0FBQztZQUN4QjtRQUVGO2tDQUFHO1FBQUNMO1FBQWFQLFNBQVN3QixXQUFXO1FBQUV4QixTQUFTMkIsaUJBQWlCO1FBQUUzQixTQUFTNEIsV0FBVztLQUFDO0lBRXhGLG9EQUFvRDtJQUNwRHJDLGdEQUFTQTttQ0FBQztZQUNSLElBQUlTLFNBQVNHLEtBQUssS0FBSyxVQUFVLGdCQUFrQixhQUFhO1lBRWhFLE1BQU0wQixhQUFhekIsT0FBT0MsVUFBVSxDQUFDO1lBRXJDLE1BQU15Qjt3REFBZTtvQkFDbkIsNEVBQTRFO29CQUM1RSxNQUFNdEIsT0FBT0MsU0FBU0MsZUFBZTtvQkFDckNGLEtBQUtHLFNBQVMsQ0FBQ0MsTUFBTSxDQUFDLFFBQVE7b0JBQzlCSixLQUFLRyxTQUFTLENBQUNFLEdBQUcsQ0FBQ1g7Z0JBQ3JCOztZQUVBMkIsV0FBV0UsZ0JBQWdCLENBQUMsVUFBVUQ7WUFDdEM7MkNBQU8sSUFBTUQsV0FBV0csbUJBQW1CLENBQUMsVUFBVUY7O1FBQ3hEO2tDQUFHO1FBQUM5QixTQUFTRyxLQUFLO0tBQUM7SUFFbkIsTUFBTThCLFdBQVcsQ0FBQzlCO1FBQ2hCRixlQUFlO1lBQUVFO1FBQU07SUFDekI7SUFFQSxNQUFNK0IsaUJBQWlCLENBQUNDO1FBQ3RCbEMsZUFBZTtZQUFFdUIsYUFBYVc7UUFBTTtJQUN0QztJQUVBLE1BQU1DLG1CQUFtQjtRQUN2Qm5DLGVBQWU7WUFBRTBCLG1CQUFtQixDQUFDM0IsU0FBUzJCLGlCQUFpQjtRQUFDO0lBQ2xFO0lBRUEsTUFBTVUsb0JBQW9CO1FBQ3hCcEMsZUFBZTtZQUFFMkIsYUFBYSxDQUFDNUIsU0FBUzRCLFdBQVc7UUFBQztJQUN0RDtJQUVBLE1BQU1VLFFBQTBCO1FBQzlCbkMsT0FBT0k7UUFDUGlCLGFBQWF4QixTQUFTd0IsV0FBVztRQUNqQ0csbUJBQW1CM0IsU0FBUzJCLGlCQUFpQjtRQUM3Q0MsYUFBYTVCLFNBQVM0QixXQUFXO1FBQ2pDSztRQUNBQztRQUNBRTtRQUNBQztJQUNGO0lBRUEscUJBQ0UsOERBQUM1QyxhQUFhOEMsUUFBUTtRQUFDRCxPQUFPQTtrQkFDM0J2Qzs7Ozs7O0FBR1AsRUFBQztBQUVELGlFQUFlTixZQUFZQSxFQUFBIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2NvbnRleHRzL1RoZW1lQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlU2V0dGluZ3MgfSBmcm9tICcuL1NldHRpbmdzQ29udGV4dCdcblxuaW50ZXJmYWNlIFRoZW1lQ29udGV4dFR5cGUge1xuICB0aGVtZTogJ2RhcmsnIHwgJ2xpZ2h0J1xuICBhY2NlbnRDb2xvcjogc3RyaW5nXG4gIGFuaW1hdGlvbnNFbmFibGVkOiBib29sZWFuXG4gIGNvbXBhY3RNb2RlOiBib29sZWFuXG4gIHNldFRoZW1lOiAodGhlbWU6ICdkYXJrJyB8ICdsaWdodCcgfCAnYXV0bycpID0+IHZvaWRcbiAgc2V0QWNjZW50Q29sb3I6IChjb2xvcjogc3RyaW5nKSA9PiB2b2lkXG4gIHRvZ2dsZUFuaW1hdGlvbnM6ICgpID0+IHZvaWRcbiAgdG9nZ2xlQ29tcGFjdE1vZGU6ICgpID0+IHZvaWRcbn1cblxuY29uc3QgVGhlbWVDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxUaGVtZUNvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpXG5cbmV4cG9ydCBjb25zdCB1c2VUaGVtZSA9ICgpID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoVGhlbWVDb250ZXh0KVxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VUaGVtZSBtdXN0IGJlIHVzZWQgd2l0aGluIGEgVGhlbWVQcm92aWRlcicpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cblxuaW50ZXJmYWNlIFRoZW1lUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbn1cblxuZXhwb3J0IGNvbnN0IFRoZW1lUHJvdmlkZXI6IFJlYWN0LkZDPFRoZW1lUHJvdmlkZXJQcm9wcz4gPSAoeyBjaGlsZHJlbiB9KSA9PiB7XG4gIGNvbnN0IHsgc2V0dGluZ3MsIHVwZGF0ZVNldHRpbmdzIH0gPSB1c2VTZXR0aW5ncygpXG5cbiAgLy8gRGV0ZXJtaW5lIGFjdHVhbCB0aGVtZSBiYXNlZCBvbiBzZXR0aW5ncyBhbmQgc3lzdGVtIHByZWZlcmVuY2VcbiAgY29uc3QgZ2V0QWN0dWFsVGhlbWUgPSAoKTogJ2RhcmsnIHwgJ2xpZ2h0JyA9PiB7XG4gICAgaWYgKHNldHRpbmdzLnRoZW1lID09PSAnYXV0bycpIHtcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICByZXR1cm4gd2luZG93Lm1hdGNoTWVkaWEoJyhwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyayknKS5tYXRjaGVzID8gJ2RhcmsnIDogJ2xpZ2h0J1xuICAgICAgfVxuICAgICAgcmV0dXJuICdkYXJrJyAvLyBEZWZhdWx0IGZhbGxiYWNrXG4gICAgfVxuICAgIHJldHVybiBzZXR0aW5ncy50aGVtZVxuICB9XG5cbiAgY29uc3QgYWN0dWFsVGhlbWUgPSBnZXRBY3R1YWxUaGVtZSgpXG5cbiAgLy8gQXBwbHkgdGhlbWUgdG8gZG9jdW1lbnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuXG4gICAgY29uc3Qgcm9vdCA9IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudFxuXG4gICAgLy8gUmVtb3ZlIGV4aXN0aW5nIHRoZW1lIGNsYXNzZXNcbiAgICByb290LmNsYXNzTGlzdC5yZW1vdmUoJ2RhcmsnLCAnbGlnaHQnKVxuXG4gICAgLy8gQWRkIGN1cnJlbnQgdGhlbWUgY2xhc3NcbiAgICByb290LmNsYXNzTGlzdC5hZGQoYWN0dWFsVGhlbWUpXG5cbiAgICAvLyBBcHBseSBhY2NlbnQgY29sb3IgQ1NTIHZhcmlhYmxlc1xuICAgIGNvbnN0IGFjY2VudENvbG9ycyA9IHtcbiAgICAgIGJsdWU6IHtcbiAgICAgICAgcHJpbWFyeTogJyMyNTYzZWInLFxuICAgICAgICBzZWNvbmRhcnk6ICcjMWU0MGFmJyxcbiAgICAgICAgbGlnaHQ6ICcjNjBhNWZhJ1xuICAgICAgfSxcbiAgICAgIHB1cnBsZToge1xuICAgICAgICBwcmltYXJ5OiAnIzdjM2FlZCcsXG4gICAgICAgIHNlY29uZGFyeTogJyM2ZDI4ZDknLFxuICAgICAgICBsaWdodDogJyNhNzhiZmEnXG4gICAgICB9LFxuICAgICAgZ3JlZW46IHtcbiAgICAgICAgcHJpbWFyeTogJyMwNTk2NjknLFxuICAgICAgICBzZWNvbmRhcnk6ICcjMDQ3ODU3JyxcbiAgICAgICAgbGlnaHQ6ICcjMzRkMzk5J1xuICAgICAgfSxcbiAgICAgIHJlZDoge1xuICAgICAgICBwcmltYXJ5OiAnI2UxMWQ0OCcsXG4gICAgICAgIHNlY29uZGFyeTogJyNiZTE4NWQnLFxuICAgICAgICBsaWdodDogJyNmYjcxODUnXG4gICAgICB9LFxuICAgICAgb3JhbmdlOiB7XG4gICAgICAgIHByaW1hcnk6ICcjZDk3NzA2JyxcbiAgICAgICAgc2Vjb25kYXJ5OiAnI2I0NTMwOScsXG4gICAgICAgIGxpZ2h0OiAnI2ZiYmYyNCdcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCBjb2xvcnMgPSBhY2NlbnRDb2xvcnNbc2V0dGluZ3MuYWNjZW50Q29sb3IgYXMga2V5b2YgdHlwZW9mIGFjY2VudENvbG9yc10gfHwgYWNjZW50Q29sb3JzLmJsdWVcblxuICAgIHJvb3Quc3R5bGUuc2V0UHJvcGVydHkoJy0tYWNjZW50LXByaW1hcnknLCBjb2xvcnMucHJpbWFyeSlcbiAgICByb290LnN0eWxlLnNldFByb3BlcnR5KCctLWFjY2VudC1zZWNvbmRhcnknLCBjb2xvcnMuc2Vjb25kYXJ5KVxuICAgIHJvb3Quc3R5bGUuc2V0UHJvcGVydHkoJy0tYWNjZW50LWxpZ2h0JywgY29sb3JzLmxpZ2h0KVxuXG4gICAgLy8gQXBwbHkgYW5pbWF0aW9ucyBzZXR0aW5nXG4gICAgaWYgKCFzZXR0aW5ncy5hbmltYXRpb25zRW5hYmxlZCkge1xuICAgICAgcm9vdC5zdHlsZS5zZXRQcm9wZXJ0eSgnLS1hbmltYXRpb24tZHVyYXRpb24nLCAnMHMnKVxuICAgICAgcm9vdC5zdHlsZS5zZXRQcm9wZXJ0eSgnLS10cmFuc2l0aW9uLWR1cmF0aW9uJywgJzBzJylcbiAgICB9IGVsc2Uge1xuICAgICAgcm9vdC5zdHlsZS5zZXRQcm9wZXJ0eSgnLS1hbmltYXRpb24tZHVyYXRpb24nLCAnMC4zcycpXG4gICAgICByb290LnN0eWxlLnNldFByb3BlcnR5KCctLXRyYW5zaXRpb24tZHVyYXRpb24nLCAnMC4ycycpXG4gICAgfVxuXG4gICAgLy8gQXBwbHkgY29tcGFjdCBtb2RlXG4gICAgaWYgKHNldHRpbmdzLmNvbXBhY3RNb2RlKSB7XG4gICAgICByb290LmNsYXNzTGlzdC5hZGQoJ2NvbXBhY3QtbW9kZScpXG4gICAgfSBlbHNlIHtcbiAgICAgIHJvb3QuY2xhc3NMaXN0LnJlbW92ZSgnY29tcGFjdC1tb2RlJylcbiAgICB9XG5cbiAgfSwgW2FjdHVhbFRoZW1lLCBzZXR0aW5ncy5hY2NlbnRDb2xvciwgc2V0dGluZ3MuYW5pbWF0aW9uc0VuYWJsZWQsIHNldHRpbmdzLmNvbXBhY3RNb2RlXSlcblxuICAvLyBMaXN0ZW4gZm9yIHN5c3RlbSB0aGVtZSBjaGFuZ2VzIHdoZW4gaW4gYXV0byBtb2RlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNldHRpbmdzLnRoZW1lICE9PSAnYXV0bycgfHwgdHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHJldHVyblxuXG4gICAgY29uc3QgbWVkaWFRdWVyeSA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJylcblxuICAgIGNvbnN0IGhhbmRsZUNoYW5nZSA9ICgpID0+IHtcbiAgICAgIC8vIEZvcmNlIHJlLXJlbmRlciBieSB1cGRhdGluZyBhIGR1bW15IHN0YXRlIG9yIHRyaWdnZXJpbmcgdGhlbWUgYXBwbGljYXRpb25cbiAgICAgIGNvbnN0IHJvb3QgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnRcbiAgICAgIHJvb3QuY2xhc3NMaXN0LnJlbW92ZSgnZGFyaycsICdsaWdodCcpXG4gICAgICByb290LmNsYXNzTGlzdC5hZGQoZ2V0QWN0dWFsVGhlbWUoKSlcbiAgICB9XG5cbiAgICBtZWRpYVF1ZXJ5LmFkZEV2ZW50TGlzdGVuZXIoJ2NoYW5nZScsIGhhbmRsZUNoYW5nZSlcbiAgICByZXR1cm4gKCkgPT4gbWVkaWFRdWVyeS5yZW1vdmVFdmVudExpc3RlbmVyKCdjaGFuZ2UnLCBoYW5kbGVDaGFuZ2UpXG4gIH0sIFtzZXR0aW5ncy50aGVtZV0pXG5cbiAgY29uc3Qgc2V0VGhlbWUgPSAodGhlbWU6ICdkYXJrJyB8ICdsaWdodCcgfCAnYXV0bycpID0+IHtcbiAgICB1cGRhdGVTZXR0aW5ncyh7IHRoZW1lIH0pXG4gIH1cblxuICBjb25zdCBzZXRBY2NlbnRDb2xvciA9IChjb2xvcjogc3RyaW5nKSA9PiB7XG4gICAgdXBkYXRlU2V0dGluZ3MoeyBhY2NlbnRDb2xvcjogY29sb3IgfSlcbiAgfVxuXG4gIGNvbnN0IHRvZ2dsZUFuaW1hdGlvbnMgPSAoKSA9PiB7XG4gICAgdXBkYXRlU2V0dGluZ3MoeyBhbmltYXRpb25zRW5hYmxlZDogIXNldHRpbmdzLmFuaW1hdGlvbnNFbmFibGVkIH0pXG4gIH1cblxuICBjb25zdCB0b2dnbGVDb21wYWN0TW9kZSA9ICgpID0+IHtcbiAgICB1cGRhdGVTZXR0aW5ncyh7IGNvbXBhY3RNb2RlOiAhc2V0dGluZ3MuY29tcGFjdE1vZGUgfSlcbiAgfVxuXG4gIGNvbnN0IHZhbHVlOiBUaGVtZUNvbnRleHRUeXBlID0ge1xuICAgIHRoZW1lOiBhY3R1YWxUaGVtZSxcbiAgICBhY2NlbnRDb2xvcjogc2V0dGluZ3MuYWNjZW50Q29sb3IsXG4gICAgYW5pbWF0aW9uc0VuYWJsZWQ6IHNldHRpbmdzLmFuaW1hdGlvbnNFbmFibGVkLFxuICAgIGNvbXBhY3RNb2RlOiBzZXR0aW5ncy5jb21wYWN0TW9kZSxcbiAgICBzZXRUaGVtZSxcbiAgICBzZXRBY2NlbnRDb2xvcixcbiAgICB0b2dnbGVBbmltYXRpb25zLFxuICAgIHRvZ2dsZUNvbXBhY3RNb2RlXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxUaGVtZUNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L1RoZW1lQ29udGV4dC5Qcm92aWRlcj5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBUaGVtZUNvbnRleHRcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU2V0dGluZ3MiLCJUaGVtZUNvbnRleHQiLCJ1bmRlZmluZWQiLCJ1c2VUaGVtZSIsImNvbnRleHQiLCJFcnJvciIsIlRoZW1lUHJvdmlkZXIiLCJjaGlsZHJlbiIsInNldHRpbmdzIiwidXBkYXRlU2V0dGluZ3MiLCJnZXRBY3R1YWxUaGVtZSIsInRoZW1lIiwid2luZG93IiwibWF0Y2hNZWRpYSIsIm1hdGNoZXMiLCJhY3R1YWxUaGVtZSIsInJvb3QiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsInJlbW92ZSIsImFkZCIsImFjY2VudENvbG9ycyIsImJsdWUiLCJwcmltYXJ5Iiwic2Vjb25kYXJ5IiwibGlnaHQiLCJwdXJwbGUiLCJncmVlbiIsInJlZCIsIm9yYW5nZSIsImNvbG9ycyIsImFjY2VudENvbG9yIiwic3R5bGUiLCJzZXRQcm9wZXJ0eSIsImFuaW1hdGlvbnNFbmFibGVkIiwiY29tcGFjdE1vZGUiLCJtZWRpYVF1ZXJ5IiwiaGFuZGxlQ2hhbmdlIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJzZXRUaGVtZSIsInNldEFjY2VudENvbG9yIiwiY29sb3IiLCJ0b2dnbGVBbmltYXRpb25zIiwidG9nZ2xlQ29tcGFjdE1vZGUiLCJ2YWx1ZSIsIlByb3ZpZGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/motion-dom","vendor-chunks/@swc","vendor-chunks/motion-utils","vendor-chunks/lodash","vendor-chunks/recharts","vendor-chunks/d3-shape","vendor-chunks/d3-scale","vendor-chunks/d3-array","vendor-chunks/d3-format","vendor-chunks/d3-interpolate","vendor-chunks/d3-time","vendor-chunks/react-smooth","vendor-chunks/react-transition-group","vendor-chunks/@babel","vendor-chunks/prop-types","vendor-chunks/recharts-scale","vendor-chunks/d3-time-format","vendor-chunks/d3-color","vendor-chunks/victory-vendor","vendor-chunks/react-is","vendor-chunks/tiny-invariant","vendor-chunks/internmap","vendor-chunks/fast-equals","vendor-chunks/decimal.js-light","vendor-chunks/d3-path","vendor-chunks/clsx","vendor-chunks/object-assign","vendor-chunks/eventemitter3"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();