/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/kaiban-superagent/route";
exports.ids = ["app/api/kaiban-superagent/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-superagent%2Froute&page=%2Fapi%2Fkaiban-superagent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-superagent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-superagent%2Froute&page=%2Fapi%2Fkaiban-superagent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-superagent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_kaiban_superagent_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/kaiban-superagent/route.ts */ \"(rsc)/./src/app/api/kaiban-superagent/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/kaiban-superagent/route\",\n        pathname: \"/api/kaiban-superagent\",\n        filename: \"route\",\n        bundlePath: \"app/api/kaiban-superagent/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/kaiban-superagent/route.ts\",\n    nextConfigOutput,\n    userland: _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_kaiban_superagent_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-superagent%2Froute&page=%2Fapi%2Fkaiban-superagent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-superagent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/kaiban-superagent/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/kaiban-superagent/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_agents_kaiban__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/agents/kaiban */ \"(rsc)/./src/lib/agents/kaiban/index.ts\");\n/**\n * KaibanJS Super Agent API Endpoint\n *\n * This API endpoint provides access to the KaibanJS-based super agent workflow\n * with support for both streaming and non-streaming responses.\n */ \n\n// Console logging utility for Kaiban API\nconst logKaibanAPI = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🌐 [KAIBAN-API] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📡 [KAIBAN-API-DATA]:`, data);\n    }\n};\n/**\n * POST - Execute KaibanJS Super Agent Workflow (Streaming)\n * Provides real-time progress updates via Server-Sent Events\n */ async function POST(request) {\n    logKaibanAPI('🚀 POST request received - Starting Kaiban Super Agent workflow');\n    try {\n        const body = await request.json();\n        const { topic, options = {} } = body;\n        logKaibanAPI('Request body parsed', {\n            topic,\n            hasOptions: !!options,\n            optionsKeys: Object.keys(options)\n        });\n        // Validate input\n        if (!topic) {\n            logKaibanAPI('❌ Validation failed - Topic is required');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Topic is required'\n            }, {\n                status: 400\n            });\n        }\n        const superAgentOptions = {\n            topic,\n            contentType: options.contentType || 'article',\n            targetWordCount: options.targetWordCount || 2000,\n            tone: options.tone || 'professional',\n            targetAudience: options.targetAudience || 'intermediate',\n            maxPrimaryResults: options.maxPrimaryResults || 6,\n            maxDeepResults: options.maxDeepResults || 4,\n            enableFactChecking: options.enableFactChecking ?? true,\n            includeSourceCitations: options.includeSourceCitations ?? true\n        };\n        logKaibanAPI('Super Agent options configured', superAgentOptions);\n        // Validate options\n        const validation = (0,_lib_agents_kaiban__WEBPACK_IMPORTED_MODULE_1__.validateSuperAgentOptions)(superAgentOptions);\n        if (!validation.isValid) {\n            logKaibanAPI('❌ Options validation failed', {\n                errors: validation.errors\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid options',\n                details: validation.errors\n            }, {\n                status: 400\n            });\n        }\n        logKaibanAPI('✅ Options validation passed');\n        // Get estimated execution time\n        const timeEstimate = (0,_lib_agents_kaiban__WEBPACK_IMPORTED_MODULE_1__.getEstimatedExecutionTime)(superAgentOptions);\n        logKaibanAPI('Execution time estimated', {\n            estimatedMinutes: timeEstimate\n        });\n        // Execute the workflow\n        logKaibanAPI('🎯 Starting workflow execution');\n        const result = await (0,_lib_agents_kaiban__WEBPACK_IMPORTED_MODULE_1__.executeSuperAgentWorkflow)(superAgentOptions);\n        logKaibanAPI('✅ Workflow execution completed', {\n            success: result.success,\n            executionTime: result.executionTime,\n            title: result.title,\n            wordCount: result.wordCount\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: result.success,\n            topic,\n            result,\n            timestamp: new Date().toISOString(),\n            systemInfo: _lib_agents_kaiban__WEBPACK_IMPORTED_MODULE_1__.KAIBAN_SUPER_AGENT_INFO\n        });\n    } catch (error) {\n        logKaibanAPI('❌ POST request failed', {\n            error: error instanceof Error ? error.message : 'Unknown error',\n            errorStack: error instanceof Error ? error.stack : undefined\n        });\n        console.error('KaibanJS Super Agent API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * PUT - Execute KaibanJS Super Agent Workflow (Non-Streaming)\n * Returns the complete result after workflow execution\n */ async function PUT(request) {\n    logKaibanAPI('🔄 PUT request received - Non-streaming Kaiban Super Agent workflow');\n    try {\n        const body = await request.json();\n        const { topic, options = {} } = body;\n        logKaibanAPI('PUT request body parsed', {\n            topic,\n            hasOptions: !!options,\n            optionsKeys: Object.keys(options)\n        });\n        // Validate input\n        if (!topic) {\n            logKaibanAPI('❌ PUT validation failed - Topic is required');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Topic is required'\n            }, {\n                status: 400\n            });\n        }\n        const superAgentOptions = {\n            topic,\n            contentType: options.contentType || 'article',\n            targetWordCount: options.targetWordCount || 2000,\n            tone: options.tone || 'professional',\n            targetAudience: options.targetAudience || 'intermediate',\n            maxPrimaryResults: options.maxPrimaryResults || 6,\n            maxDeepResults: options.maxDeepResults || 4,\n            enableFactChecking: options.enableFactChecking ?? true,\n            includeSourceCitations: options.includeSourceCitations ?? true\n        };\n        logKaibanAPI('PUT Super Agent options configured', superAgentOptions);\n        // Validate options\n        const validation = (0,_lib_agents_kaiban__WEBPACK_IMPORTED_MODULE_1__.validateSuperAgentOptions)(superAgentOptions);\n        if (!validation.isValid) {\n            logKaibanAPI('❌ PUT options validation failed', {\n                errors: validation.errors\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid options',\n                details: validation.errors\n            }, {\n                status: 400\n            });\n        }\n        logKaibanAPI('✅ PUT options validation passed');\n        // Execute workflow\n        logKaibanAPI('🎯 Starting PUT workflow execution');\n        const result = await (0,_lib_agents_kaiban__WEBPACK_IMPORTED_MODULE_1__.executeSuperAgentWorkflow)(superAgentOptions);\n        logKaibanAPI('✅ PUT workflow execution completed', {\n            success: result.success,\n            executionTime: result.executionTime,\n            title: result.title,\n            wordCount: result.wordCount\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            topic,\n            result,\n            timestamp: new Date().toISOString(),\n            systemInfo: _lib_agents_kaiban__WEBPACK_IMPORTED_MODULE_1__.KAIBAN_SUPER_AGENT_INFO\n        });\n    } catch (error) {\n        logKaibanAPI('❌ PUT request failed', {\n            error: error instanceof Error ? error.message : 'Unknown error',\n            errorStack: error instanceof Error ? error.stack : undefined\n        });\n        console.error('KaibanJS Super Agent API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error occurred',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * GET - Get KaibanJS Super Agent System Information\n * Returns system capabilities and configuration options\n */ async function GET() {\n    logKaibanAPI('ℹ️ GET request received - System information requested');\n    try {\n        logKaibanAPI('✅ Returning system information and capabilities');\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            systemInfo: _lib_agents_kaiban__WEBPACK_IMPORTED_MODULE_1__.KAIBAN_SUPER_AGENT_INFO,\n            endpoints: {\n                streaming: {\n                    method: 'POST',\n                    description: 'Execute workflow with real-time progress updates',\n                    contentType: 'text/event-stream'\n                },\n                nonStreaming: {\n                    method: 'PUT',\n                    description: 'Execute workflow and return complete result',\n                    contentType: 'application/json'\n                },\n                info: {\n                    method: 'GET',\n                    description: 'Get system information and capabilities',\n                    contentType: 'application/json'\n                }\n            },\n            requestFormat: {\n                topic: 'string (required)',\n                options: {\n                    contentType: 'article | blog-post | research-paper | comprehensive-guide',\n                    targetWordCount: 'number (100-10000)',\n                    tone: 'professional | casual | academic | conversational',\n                    targetAudience: 'beginner | intermediate | expert | general',\n                    maxPrimaryResults: 'number (1-20)',\n                    maxDeepResults: 'number (1-10)',\n                    enableFactChecking: 'boolean',\n                    includeSourceCitations: 'boolean'\n                }\n            },\n            examples: {\n                basicRequest: {\n                    topic: 'Artificial Intelligence in Healthcare',\n                    options: {\n                        contentType: 'article',\n                        targetWordCount: 2000,\n                        tone: 'professional'\n                    }\n                },\n                advancedRequest: {\n                    topic: 'The Future of Renewable Energy',\n                    options: {\n                        contentType: 'comprehensive-guide',\n                        targetWordCount: 3000,\n                        tone: 'academic',\n                        targetAudience: 'expert',\n                        maxPrimaryResults: 8,\n                        maxDeepResults: 6,\n                        enableFactChecking: true,\n                        includeSourceCitations: true\n                    }\n                }\n            }\n        });\n    } catch (error) {\n        logKaibanAPI('❌ GET request failed', {\n            error: error instanceof Error ? error.message : 'Unknown error',\n            errorStack: error instanceof Error ? error.stack : undefined\n        });\n        console.error('KaibanJS Super Agent Info API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * OPTIONS - Handle CORS preflight requests\n */ async function OPTIONS() {\n    logKaibanAPI('🔧 OPTIONS request received - CORS preflight');\n    return new Response(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, POST, PUT, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization',\n            'Access-Control-Max-Age': '86400'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/kaiban-superagent/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban/agents.ts":
/*!*****************************************!*\
  !*** ./src/lib/agents/kaiban/agents.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   agentConfig: () => (/* binding */ agentConfig),\n/* harmony export */   contentGenerationAgent: () => (/* binding */ contentGenerationAgent),\n/* harmony export */   contentStrategyAgent: () => (/* binding */ contentStrategyAgent),\n/* harmony export */   deepResearchAgent: () => (/* binding */ deepResearchAgent),\n/* harmony export */   gapAnalysisAgent: () => (/* binding */ gapAnalysisAgent),\n/* harmony export */   primaryResearchAgent: () => (/* binding */ primaryResearchAgent),\n/* harmony export */   qualityAssuranceAgent: () => (/* binding */ qualityAssuranceAgent),\n/* harmony export */   superAgentTeam: () => (/* binding */ superAgentTeam),\n/* harmony export */   topicAnalysisAgent: () => (/* binding */ topicAnalysisAgent)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/**\n * KaibanJS Agents for Super Agent Workflow\n *\n * This file defines all the specialized agents that will work together\n * to execute the super agent workflow using KaibanJS framework.\n */ \n// Console logging utility for Kaiban Super Agent\nconst logKaibanAgent = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🤖 [KAIBAN-AGENT] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📊 [KAIBAN-DATA]:`, data);\n    }\n};\n// Log agent initialization\nlogKaibanAgent('Initializing Kaiban Super Agent System', {\n    totalAgents: 7,\n    models: [\n        'qwen/qwen3-235b-a22b-04-28',\n        'gemini-2.0-flash-lite'\n    ],\n    providers: [\n        'openai',\n        'google'\n    ]\n});\n/**\n * Topic Analysis Agent\n * Responsible for analyzing the input topic and generating research strategies\n * Uses Qwen model for advanced reasoning and topic analysis\n */ logKaibanAgent('Creating Topic Analysis Agent', {\n    name: 'Topic Analyzer',\n    model: 'qwen/qwen3-235b-a22b-04-28',\n    provider: 'openai',\n    maxIterations: 20\n});\nconst topicAnalysisAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'Topic Analyzer',\n    role: 'Advanced Topic Analysis Specialist',\n    goal: 'Conduct comprehensive topic analysis using chain-of-thought reasoning to create strategic research frameworks and actionable content strategies',\n    background: `Distinguished research analyst and strategic consultant with PhD in Information Science and 15+ years of expertise in advanced topic analysis, competitive intelligence, and content strategy development.\n\n    **Core Expertise:**\n    - Advanced topic deconstruction and systematic analysis methodologies\n    - Keyword research and semantic analysis using cutting-edge techniques\n    - Competitive landscape analysis and market positioning strategies\n    - Audience profiling and psychographic analysis\n    - Content trend analysis and forward-looking market intelligence\n    - Research query optimization and search strategy development\n\n    **Specialized Skills:**\n    - Chain-of-thought reasoning for complex analytical tasks\n    - Multi-dimensional topic mapping and content angle identification\n    - Advanced search operator techniques and query formulation\n    - Content gap analysis and opportunity identification\n    - Strategic planning for multi-phase research initiatives\n    - Data-driven decision making and evidence-based recommendations\n\n    **Professional Background:**\n    - Former Head of Research at leading digital marketing agencies\n    - Published researcher in content strategy and information retrieval\n    - Consultant for Fortune 500 companies on content strategy\n    - Expert in 2025 content trends and emerging market dynamics\n    - Specialist in Google Search API optimization and advanced search techniques\n\n    **Enhanced Capabilities:**\n    - Powered by Qwen model for superior analytical reasoning and complex problem-solving\n    - Advanced pattern recognition for identifying content opportunities\n    - Systematic methodology for breaking down complex topics into actionable components\n    - Strategic thinking for long-term content planning and competitive positioning`,\n    tools: [],\n    llmConfig: {\n        provider: 'openai',\n        model: 'qwen/qwen3-235b-a22b-04-28',\n        apiBaseUrl: 'https://openrouter.ai/api/v1',\n        apiKey: \"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\"\n    },\n    maxIterations: 20,\n    forceFinalAnswer: true\n});\nlogKaibanAgent('Topic Analysis Agent created successfully');\n/**\n * Content Strategy Agent\n * Develops content strategy and structure based on topic analysis\n * Uses Gemini 2.0 Flash Lite for strategic planning and content architecture\n */ logKaibanAgent('Creating Content Strategy Agent', {\n    name: 'Content Strategist',\n    model: 'gemini-2.0-flash-lite',\n    provider: 'google',\n    maxIterations: 12\n});\nconst contentStrategyAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'Content Strategist',\n    role: 'Strategic Content Architecture Director',\n    goal: 'Develop comprehensive, data-driven content strategies that maximize audience engagement, search visibility, and conversion effectiveness through systematic strategic planning',\n    background: `Elite content strategist and digital marketing executive with 12+ years of expertise in content architecture, audience psychology, and engagement optimization. Former VP of Content Strategy at leading digital agencies and Fortune 500 companies.\n\n    **Strategic Expertise:**\n    - Advanced content architecture and information hierarchy design\n    - Audience psychology and behavioral analysis for content optimization\n    - SEO strategy integration and search engine optimization planning\n    - Content differentiation and competitive positioning strategies\n    - Engagement optimization and reader retention techniques\n    - Multi-channel content strategy and distribution planning\n\n    **Specialized Skills:**\n    - Systematic content structure development and outline creation\n    - Message hierarchy and communication strategy design\n    - Content flow optimization and narrative arc development\n    - Call-to-action strategy and conversion optimization\n    - Brand voice development and tone consistency frameworks\n    - Content performance prediction and success metrics planning\n\n    **Professional Achievements:**\n    - Developed content strategies resulting in 300%+ engagement increases\n    - Led content teams for major publications with millions of monthly readers\n    - Expert in 2025 content trends and emerging engagement techniques\n    - Specialist in audience-centric content design and user experience optimization\n    - Published thought leader in content strategy and digital marketing\n\n    **Core Methodologies:**\n    - Data-driven strategy development using analytics and research insights\n    - Systematic approach to content planning and strategic framework creation\n    - Advanced understanding of content psychology and reader behavior\n    - Expert-level knowledge of SEO integration and search optimization strategies`,\n    tools: [],\n    llmConfig: {\n        provider: 'google',\n        model: 'gemini-2.0-flash-lite',\n        apiKey: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\"\n    },\n    maxIterations: 12,\n    forceFinalAnswer: true\n});\nlogKaibanAgent('Content Strategy Agent created successfully');\n/**\n * Primary Research Agent\n * Conducts initial research using search queries and data extraction\n * Uses Gemini 2.0 Flash Lite for efficient research and data processing\n */ logKaibanAgent('Creating Primary Research Agent', {\n    name: 'Primary Researcher',\n    model: 'gemini-2.0-flash-lite',\n    provider: 'google',\n    maxIterations: 20\n});\nconst primaryResearchAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'Primary Researcher',\n    role: 'Primary Research Specialist',\n    goal: 'Conduct comprehensive primary research using Google Search API to gather foundational information',\n    background: `Research scientist with expertise in information retrieval, data analysis,\n    and source evaluation. PhD in Information Science with 8+ years of experience in\n    academic and commercial research. Excels at finding relevant, high-quality sources\n    and extracting key insights from large datasets. Uses Google Programmable Search Engine\n    API for comprehensive web research instead of Tavily search.`,\n    tools: [],\n    llmConfig: {\n        provider: 'google',\n        model: 'gemini-2.0-flash-lite',\n        apiKey: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\"\n    },\n    maxIterations: 20,\n    forceFinalAnswer: true\n});\nlogKaibanAgent('Primary Research Agent created successfully');\n/**\n * Gap Analysis Agent\n * Identifies gaps in research data and determines additional research needs\n * Uses Qwen model for advanced reasoning and complex gap analysis\n */ logKaibanAgent('Creating Gap Analysis Agent', {\n    name: 'Gap Analyst',\n    model: 'qwen/qwen3-235b-a22b-04-28',\n    provider: 'openai',\n    maxIterations: 25\n});\nconst gapAnalysisAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'Gap Analyst',\n    role: 'Research Gap Analyst',\n    goal: 'Identify information gaps and determine strategic research priorities',\n    background: `Academic researcher and consultant specializing in systematic reviews and\n    gap analysis. Professor of Research Methodology with 12+ years of experience in\n    identifying research gaps and designing targeted research strategies. Expert in\n    evaluating information completeness and quality. Enhanced with advanced reasoning\n    capabilities using Qwen model for superior analytical thinking.`,\n    tools: [],\n    llmConfig: {\n        provider: 'openai',\n        model: 'qwen/qwen3-235b-a22b-04-28',\n        apiBaseUrl: 'https://openrouter.ai/api/v1',\n        apiKey: \"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\"\n    },\n    maxIterations: 25,\n    forceFinalAnswer: true\n});\nlogKaibanAgent('Gap Analysis Agent created successfully');\n/**\n * Deep Research Agent\n * Conducts targeted deep research based on gap analysis findings\n * Uses Gemini 2.0 Flash Lite for deep research and information extraction\n */ logKaibanAgent('Creating Deep Research Agent', {\n    name: 'Deep Researcher',\n    model: 'gemini-2.0-flash-lite',\n    provider: 'google',\n    maxIterations: 25\n});\nconst deepResearchAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'Deep Researcher',\n    role: 'Deep Research Specialist',\n    goal: 'Conduct targeted deep research using Google Search API to fill identified information gaps',\n    background: `Investigative researcher with expertise in specialized research methodologies\n    and advanced information retrieval techniques. 15+ years of experience in conducting\n    deep-dive research for academic institutions, think tanks, and consulting firms.\n    Known for uncovering hard-to-find information and expert insights. Uses Google Search\n    API with advanced search operators for comprehensive research instead of Tavily.`,\n    tools: [],\n    llmConfig: {\n        provider: 'google',\n        model: 'gemini-2.0-flash-lite',\n        apiKey: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\"\n    },\n    maxIterations: 25,\n    forceFinalAnswer: true\n});\nlogKaibanAgent('Deep Research Agent created successfully');\n/**\n * Content Generation Agent\n * Generates high-quality content based on all research data\n * Uses Gemini 2.0 Flash Lite for creative content generation and writing\n */ logKaibanAgent('Creating Content Generation Agent', {\n    name: 'Content Generator',\n    model: 'gemini-2.0-flash-lite',\n    provider: 'google',\n    maxIterations: 20\n});\nconst contentGenerationAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'Content Generator',\n    role: 'Senior Content Creator',\n    goal: 'Create compelling, well-researched content that engages and informs readers',\n    background: `Award-winning content creator and journalist with 12+ years of experience\n    writing for major publications and digital platforms. Specializes in transforming\n    complex research into accessible, engaging content. Expert in various content formats\n    and writing styles, with a track record of creating viral and highly-shared content.`,\n    tools: [],\n    llmConfig: {\n        provider: 'google',\n        model: 'gemini-2.0-flash-lite',\n        apiKey: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\"\n    },\n    maxIterations: 20,\n    forceFinalAnswer: true\n});\nlogKaibanAgent('Content Generation Agent created successfully');\n/**\n * Quality Assurance Agent\n * Reviews and validates the generated content for quality and accuracy\n * Uses Qwen model for advanced reasoning and comprehensive quality analysis\n */ logKaibanAgent('Creating Quality Assurance Agent', {\n    name: 'Quality Assurance',\n    model: 'qwen/qwen3-235b-a22b-04-28',\n    provider: 'openai',\n    maxIterations: 25\n});\nconst qualityAssuranceAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'Quality Assurance',\n    role: 'Quality Assurance Director',\n    goal: 'Ensure content meets the highest standards of quality, accuracy, and effectiveness',\n    background: `Quality assurance expert with extensive experience in content review,\n    fact-checking, and editorial processes. Former editor-in-chief of several academic\n    journals and digital publications. PhD in Communications with specialization in\n    content quality metrics and reader engagement analysis. Known for meticulous\n    attention to detail and comprehensive quality frameworks. Enhanced with advanced\n    reasoning capabilities using Qwen model for superior analytical thinking.`,\n    tools: [],\n    llmConfig: {\n        provider: 'openai',\n        model: 'qwen/qwen3-235b-a22b-04-28',\n        apiBaseUrl: 'https://openrouter.ai/api/v1',\n        apiKey: \"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\"\n    },\n    maxIterations: 25,\n    forceFinalAnswer: true\n});\nlogKaibanAgent('Quality Assurance Agent created successfully');\n/**\n * Export all agents for use in the team configuration\n */ logKaibanAgent('Exporting Super Agent Team', {\n    totalAgents: 7,\n    agentNames: [\n        'Topic Analyzer',\n        'Content Strategist',\n        'Primary Researcher',\n        'Gap Analyst',\n        'Deep Researcher',\n        'Content Generator',\n        'Quality Assurance'\n    ]\n});\nconst superAgentTeam = {\n    topicAnalysisAgent,\n    contentStrategyAgent,\n    primaryResearchAgent,\n    gapAnalysisAgent,\n    deepResearchAgent,\n    contentGenerationAgent,\n    qualityAssuranceAgent\n};\n/**\n * Agent configuration for easy access\n */ logKaibanAgent('Creating Agent Configuration', {\n    totalAgents: 7,\n    configurationComplete: true\n});\nconst agentConfig = {\n    agents: [\n        topicAnalysisAgent,\n        contentStrategyAgent,\n        primaryResearchAgent,\n        gapAnalysisAgent,\n        deepResearchAgent,\n        contentGenerationAgent,\n        qualityAssuranceAgent\n    ],\n    agentNames: [\n        'Topic Analyzer',\n        'Content Strategist',\n        'Primary Researcher',\n        'Gap Analyst',\n        'Deep Researcher',\n        'Content Generator',\n        'Quality Assurance'\n    ]\n};\nlogKaibanAgent('Kaiban Super Agent System initialization complete', {\n    status: 'ready',\n    totalAgents: 7,\n    highReasoningAgents: 3,\n    standardAgents: 4,\n    timestamp: new Date().toISOString()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban/agents.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban/index.ts":
/*!****************************************!*\
  !*** ./src/lib/agents/kaiban/index.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentAnalysisTool: () => (/* reexport safe */ _tools__WEBPACK_IMPORTED_MODULE_0__.ContentAnalysisTool),\n/* harmony export */   ContentGenerationTool: () => (/* reexport safe */ _tools__WEBPACK_IMPORTED_MODULE_0__.ContentGenerationTool),\n/* harmony export */   DataProcessingTool: () => (/* reexport safe */ _tools__WEBPACK_IMPORTED_MODULE_0__.DataProcessingTool),\n/* harmony export */   GoogleSearchTool: () => (/* reexport safe */ _tools__WEBPACK_IMPORTED_MODULE_0__.GoogleSearchTool),\n/* harmony export */   KAIBAN_SUPER_AGENT_INFO: () => (/* binding */ KAIBAN_SUPER_AGENT_INFO),\n/* harmony export */   KaibanSuperAgentTeam: () => (/* reexport safe */ _super_agent_team__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   SuperAgentOptions: () => (/* reexport safe */ _super_agent_team__WEBPACK_IMPORTED_MODULE_3__.SuperAgentOptions),\n/* harmony export */   SuperAgentResult: () => (/* reexport safe */ _super_agent_team__WEBPACK_IMPORTED_MODULE_3__.SuperAgentResult),\n/* harmony export */   WORKFLOW_PHASES: () => (/* binding */ WORKFLOW_PHASES),\n/* harmony export */   agentConfig: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig),\n/* harmony export */   contentGenerationAgent: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.contentGenerationAgent),\n/* harmony export */   contentGenerationTask: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.contentGenerationTask),\n/* harmony export */   contentStrategyAgent: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.contentStrategyAgent),\n/* harmony export */   contentStrategyTask: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.contentStrategyTask),\n/* harmony export */   createSuperAgentTeam: () => (/* reexport safe */ _super_agent_team__WEBPACK_IMPORTED_MODULE_3__.createSuperAgentTeam),\n/* harmony export */   deepResearchAgent: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.deepResearchAgent),\n/* harmony export */   deepResearchTask: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.deepResearchTask),\n/* harmony export */   executeSuperAgentWorkflow: () => (/* reexport safe */ _super_agent_team__WEBPACK_IMPORTED_MODULE_3__.executeSuperAgentWorkflow),\n/* harmony export */   gapAnalysisAgent: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.gapAnalysisAgent),\n/* harmony export */   gapAnalysisTask: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.gapAnalysisTask),\n/* harmony export */   getEstimatedExecutionTime: () => (/* binding */ getEstimatedExecutionTime),\n/* harmony export */   getPhaseDisplayName: () => (/* binding */ getPhaseDisplayName),\n/* harmony export */   getPhaseProgress: () => (/* binding */ getPhaseProgress),\n/* harmony export */   primaryResearchAgent: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.primaryResearchAgent),\n/* harmony export */   primaryResearchTask: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.primaryResearchTask),\n/* harmony export */   qualityAssuranceAgent: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.qualityAssuranceAgent),\n/* harmony export */   qualityAssuranceTask: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.qualityAssuranceTask),\n/* harmony export */   quickStartSuperAgent: () => (/* binding */ quickStartSuperAgent),\n/* harmony export */   superAgentTasks: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.superAgentTasks),\n/* harmony export */   superAgentTeam: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam),\n/* harmony export */   taskConfig: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig),\n/* harmony export */   topicAnalysisAgent: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.topicAnalysisAgent),\n/* harmony export */   topicAnalysisTask: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.topicAnalysisTask),\n/* harmony export */   validateSuperAgentOptions: () => (/* binding */ validateSuperAgentOptions)\n/* harmony export */ });\n/* harmony import */ var _tools__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tools */ \"(rsc)/./src/lib/agents/kaiban/tools.ts\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/agents/kaiban/agents.ts\");\n/* harmony import */ var _tasks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tasks */ \"(rsc)/./src/lib/agents/kaiban/tasks.ts\");\n/* harmony import */ var _super_agent_team__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./super-agent-team */ \"(rsc)/./src/lib/agents/kaiban/super-agent-team.ts\");\n/**\n * KaibanJS Super Agent System - Main Export\n * \n * This file exports all the components of the KaibanJS-based super agent system\n * for easy import and use throughout the application.\n */ // Export tools\n\n// Export agents\n\n// Export tasks\n\n// Export team and workflow\n\n// Export default team creator\n\n/**\n * KaibanJS Super Agent System Information\n */ const KAIBAN_SUPER_AGENT_INFO = {\n    name: 'KaibanJS Super Agent System',\n    version: '1.0.0',\n    description: 'A comprehensive multi-agent content creation system built with KaibanJS',\n    agents: {\n        count: 7,\n        names: [\n            'Topic Analyzer - Topic Analysis Specialist',\n            'Content Strategist - Content Strategy Director',\n            'Primary Researcher - Primary Research Specialist',\n            'Gap Analyst - Research Gap Analyst',\n            'Deep Researcher - Deep Research Specialist',\n            'Content Generator - Senior Content Creator',\n            'Quality Assurance - Quality Assurance Director'\n        ]\n    },\n    workflow: {\n        phases: 7,\n        steps: [\n            'Topic Analysis',\n            'Content Strategy Development',\n            'Primary Research',\n            'Gap Analysis',\n            'Deep Research',\n            'Content Generation',\n            'Quality Assurance'\n        ]\n    },\n    capabilities: [\n        'Comprehensive topic analysis',\n        'Strategic content planning',\n        'Multi-source research',\n        'Gap identification and filling',\n        'High-quality content generation',\n        'Automated quality assurance',\n        'Real-time progress tracking',\n        'Source attribution and citations'\n    ],\n    supportedContentTypes: [\n        'article',\n        'blog-post',\n        'research-paper',\n        'comprehensive-guide'\n    ],\n    supportedTones: [\n        'professional',\n        'casual',\n        'academic',\n        'conversational'\n    ],\n    supportedAudiences: [\n        'beginner',\n        'intermediate',\n        'expert',\n        'general'\n    ]\n};\n/**\n * Quick Start Function\n * Provides a simple way to execute the workflow with minimal configuration\n */ async function quickStartSuperAgent(topic, options, onProgress) {\n    const defaultOptions = {\n        topic,\n        contentType: 'article',\n        targetWordCount: 2000,\n        tone: 'professional',\n        targetAudience: 'intermediate',\n        maxPrimaryResults: 6,\n        maxDeepResults: 4,\n        enableFactChecking: true,\n        includeSourceCitations: true,\n        ...options\n    };\n    return await executeSuperAgentWorkflow(defaultOptions, onProgress);\n}\n/**\n * Utility Functions\n */ /**\n * Validate Super Agent Options\n */ function validateSuperAgentOptions(options) {\n    const errors = [];\n    if (!options.topic || options.topic.trim().length === 0) {\n        errors.push('Topic is required and cannot be empty');\n    }\n    if (options.topic && options.topic.length < 3) {\n        errors.push('Topic must be at least 3 characters long');\n    }\n    if (options.targetWordCount && (options.targetWordCount < 100 || options.targetWordCount > 10000)) {\n        errors.push('Target word count must be between 100 and 10,000');\n    }\n    if (options.maxPrimaryResults && (options.maxPrimaryResults < 1 || options.maxPrimaryResults > 20)) {\n        errors.push('Max primary results must be between 1 and 20');\n    }\n    if (options.maxDeepResults && (options.maxDeepResults < 1 || options.maxDeepResults > 10)) {\n        errors.push('Max deep results must be between 1 and 10');\n    }\n    const validContentTypes = [\n        'article',\n        'blog-post',\n        'research-paper',\n        'comprehensive-guide'\n    ];\n    if (options.contentType && !validContentTypes.includes(options.contentType)) {\n        errors.push(`Content type must be one of: ${validContentTypes.join(', ')}`);\n    }\n    const validTones = [\n        'professional',\n        'casual',\n        'academic',\n        'conversational'\n    ];\n    if (options.tone && !validTones.includes(options.tone)) {\n        errors.push(`Tone must be one of: ${validTones.join(', ')}`);\n    }\n    const validAudiences = [\n        'beginner',\n        'intermediate',\n        'expert',\n        'general'\n    ];\n    if (options.targetAudience && !validAudiences.includes(options.targetAudience)) {\n        errors.push(`Target audience must be one of: ${validAudiences.join(', ')}`);\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n/**\n * Get Estimated Execution Time\n */ function getEstimatedExecutionTime(options) {\n    const baseTime = 2; // Base time per phase in minutes\n    const researchMultiplier = (options.maxPrimaryResults || 6) * 0.5 + (options.maxDeepResults || 4) * 0.8;\n    const wordCountMultiplier = (options.targetWordCount || 2000) / 1000;\n    const breakdown = {\n        'Topic Analysis': baseTime,\n        'Content Strategy': baseTime,\n        'Primary Research': baseTime + researchMultiplier,\n        'Gap Analysis': baseTime * 0.8,\n        'Deep Research': baseTime + researchMultiplier * 0.6,\n        'Content Generation': baseTime + wordCountMultiplier,\n        'Quality Assurance': baseTime * 0.8\n    };\n    const estimatedMinutes = Object.values(breakdown).reduce((sum, time)=>sum + time, 0);\n    return {\n        estimatedMinutes: Math.round(estimatedMinutes),\n        breakdown\n    };\n}\n/**\n * Progress Tracking Utilities\n */ const WORKFLOW_PHASES = [\n    'topic-analysis',\n    'content-strategy',\n    'primary-research',\n    'gap-analysis',\n    'deep-research',\n    'content-generation',\n    'quality-assurance'\n];\nfunction getPhaseDisplayName(phase) {\n    const displayNames = {\n        'topic-analysis': 'Topic Analysis',\n        'content-strategy': 'Content Strategy',\n        'primary-research': 'Primary Research',\n        'gap-analysis': 'Gap Analysis',\n        'deep-research': 'Deep Research',\n        'content-generation': 'Content Generation',\n        'quality-assurance': 'Quality Assurance'\n    };\n    return displayNames[phase];\n}\nfunction getPhaseProgress(phase) {\n    const phaseIndex = WORKFLOW_PHASES.indexOf(phase);\n    return Math.round((phaseIndex + 1) / WORKFLOW_PHASES.length * 100);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban/super-agent-team.ts":
/*!***************************************************!*\
  !*** ./src/lib/agents/kaiban/super-agent-team.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSuperAgentTeam: () => (/* binding */ createSuperAgentTeam),\n/* harmony export */   \"default\": () => (/* binding */ createSuperAgentTeam),\n/* harmony export */   executeSuperAgentWorkflow: () => (/* binding */ executeSuperAgentWorkflow)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/agents/kaiban/agents.ts\");\n/* harmony import */ var _tasks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tasks */ \"(rsc)/./src/lib/agents/kaiban/tasks.ts\");\n/**\n * KaibanJS Super Agent Team\n *\n * This file creates and configures the main KaibanJS team that orchestrates\n * the entire super agent workflow with all agents and tasks.\n */ \n\n\n// Console logging utility for Kaiban Super Agent Team\nconst logKaibanTeam = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🚀 [KAIBAN-TEAM] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📈 [KAIBAN-TEAM-DATA]:`, data);\n    }\n};\n// Console logging utility for workflow progress\nconst logKaibanProgress = (phase, progress, message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`⚡ [KAIBAN-PROGRESS] ${timestamp}: [${phase}] ${progress}% - ${message}`);\n    if (data) {\n        console.log(`📊 [KAIBAN-PROGRESS-DATA]:`, data);\n    }\n};\n/**\n * Create Super Agent Team\n * Factory function to create a configured KaibanJS team\n */ function createSuperAgentTeam(options, onProgress) {\n    logKaibanTeam('Creating Super Agent Team', {\n        topic: options.topic,\n        contentType: options.contentType,\n        targetWordCount: options.targetWordCount,\n        tone: options.tone,\n        targetAudience: options.targetAudience\n    });\n    // Set default options\n    const defaultOptions = {\n        topic: options.topic,\n        contentType: options.contentType || 'article',\n        targetWordCount: options.targetWordCount || 2000,\n        tone: options.tone || 'professional',\n        targetAudience: options.targetAudience || 'intermediate',\n        maxPrimaryResults: options.maxPrimaryResults || 6,\n        maxDeepResults: options.maxDeepResults || 4,\n        enableFactChecking: options.enableFactChecking ?? true,\n        includeSourceCitations: options.includeSourceCitations ?? true\n    };\n    logKaibanTeam('Default options configured', defaultOptions);\n    // Create the team with all agents and tasks\n    logKaibanTeam('Initializing KaibanJS Team', {\n        teamName: 'Super Agent Content Creation Team',\n        totalAgents: 7,\n        totalTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.superAgentTasks.length,\n        memoryEnabled: true\n    });\n    const team = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Team({\n        name: 'Super Agent Content Creation Team',\n        agents: [\n            _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.topicAnalysisAgent,\n            _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.contentStrategyAgent,\n            _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.primaryResearchAgent,\n            _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.gapAnalysisAgent,\n            _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.deepResearchAgent,\n            _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.contentGenerationAgent,\n            _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.qualityAssuranceAgent\n        ],\n        tasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.superAgentTasks,\n        inputs: {\n            topic: defaultOptions.topic,\n            contentType: defaultOptions.contentType,\n            targetWordCount: defaultOptions.targetWordCount,\n            tone: defaultOptions.tone,\n            targetAudience: defaultOptions.targetAudience,\n            maxPrimaryResults: defaultOptions.maxPrimaryResults,\n            maxDeepResults: defaultOptions.maxDeepResults,\n            enableFactChecking: defaultOptions.enableFactChecking,\n            includeSourceCitations: defaultOptions.includeSourceCitations\n        },\n        env: {\n            OPENAI_API_KEY: process.env.OPENAI_API_KEY || '',\n            OPENROUTER_API_KEY: \"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\" || 0,\n            GOOGLE_SEARCH_API_KEY: \"AIzaSyBlQ7HhWbY37GYbeV9ZJZmTUucspF2KbXE\" || 0,\n            GOOGLE_SEARCH_ENGINE_ID: \"830840f1a0eaf4acf\" || 0,\n            GEMINI_API_KEY: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\" || 0,\n            // OpenRouter configuration for high reasoning phases\n            OPENROUTER_BASE_URL: 'https://openrouter.ai/api/v1'\n        },\n        memory: true // Enable memory for task result passing\n    });\n    logKaibanTeam('KaibanJS Team created successfully', {\n        teamConfigured: true,\n        inputsSet: true,\n        environmentConfigured: true\n    });\n    // Set up progress tracking if callback provided\n    if (onProgress) {\n        logKaibanTeam('Setting up progress tracking', {\n            progressCallbackProvided: true,\n            trackingEnabled: true\n        });\n        const useStore = team.useStore();\n        // Subscribe to workflow logs for progress tracking\n        useStore.subscribe((state)=>state.workflowLogs, (newLogs, previousLogs)=>{\n            if (newLogs.length > previousLogs.length) {\n                const latestLog = newLogs[newLogs.length - 1];\n                logKaibanProgress('workflow-update', 0, 'New workflow log received', {\n                    logType: latestLog.logType,\n                    totalLogs: newLogs.length,\n                    previousLogs: previousLogs.length\n                });\n                if (latestLog.logType === 'TaskStatusUpdate') {\n                    const { task, agent } = latestLog;\n                    const progress = calculateProgress(task, newLogs);\n                    const message = `${agent.name}: ${getTaskStatusMessage(task.status)}`;\n                    const phase = getPhaseFromTask(task);\n                    logKaibanProgress(phase, progress, message, {\n                        taskName: task.description,\n                        agentName: agent.name,\n                        taskStatus: task.status,\n                        calculatedProgress: progress\n                    });\n                    onProgress(phase, progress, message);\n                }\n            }\n        });\n    } else {\n        logKaibanTeam('No progress callback provided - running without progress tracking');\n    }\n    logKaibanTeam('Team setup complete - ready for execution');\n    return team;\n}\n/**\n * Execute Super Agent Workflow\n * Main function to execute the complete workflow\n */ async function executeSuperAgentWorkflow(options, onProgress) {\n    const startTime = Date.now();\n    logKaibanTeam('🚀 Starting Super Agent Workflow Execution', {\n        topic: options.topic,\n        contentType: options.contentType || 'article',\n        targetWordCount: options.targetWordCount || 2000,\n        tone: options.tone || 'professional',\n        startTime: new Date(startTime).toISOString(),\n        progressTrackingEnabled: !!onProgress\n    });\n    try {\n        // Create and start the team\n        logKaibanTeam('Creating team for workflow execution');\n        const team = createSuperAgentTeam(options, onProgress);\n        logKaibanTeam('🎯 Starting team execution - workflow beginning');\n        const result = await team.start();\n        const executionTime = Date.now() - startTime;\n        logKaibanTeam('✅ Team execution completed', {\n            executionTimeMs: executionTime,\n            executionTimeSeconds: Math.round(executionTime / 1000),\n            success: true\n        });\n        // Extract results from the workflow\n        logKaibanTeam('Extracting workflow results');\n        const workflowResult = extractWorkflowResults(result, team);\n        const finalResult = {\n            success: true,\n            topic: options.topic,\n            executionTime,\n            ...workflowResult\n        };\n        logKaibanTeam('🎉 Super Agent Workflow completed successfully', {\n            topic: options.topic,\n            executionTime,\n            title: finalResult.title,\n            wordCount: finalResult.wordCount,\n            qualityScore: finalResult.qualityScore,\n            sourcesUsed: finalResult.sourcesUsed\n        });\n        return finalResult;\n    } catch (error) {\n        const executionTime = Date.now() - startTime;\n        logKaibanTeam('❌ Super Agent Workflow failed', {\n            topic: options.topic,\n            executionTime,\n            error: error instanceof Error ? error.message : 'Unknown error',\n            errorStack: error instanceof Error ? error.stack : undefined\n        });\n        return {\n            success: false,\n            topic: options.topic,\n            executionTime,\n            error: error instanceof Error ? error.message : 'Unknown error occurred'\n        };\n    }\n}\n/**\n * Helper Functions\n */ function calculateProgress(task, logs) {\n    // Calculate progress based on task completion and current phase\n    const totalTasks = 7;\n    const completedTasks = logs.filter((log)=>log.logType === 'TaskStatusUpdate' && log.task.status === 'DONE').length;\n    const progress = Math.round(completedTasks / totalTasks * 100);\n    logKaibanTeam('Progress calculated', {\n        totalTasks,\n        completedTasks,\n        progress,\n        currentTaskDescription: task.description,\n        currentTaskStatus: task.status\n    });\n    return progress;\n}\nfunction getTaskStatusMessage(status) {\n    const statusMessages = {\n        'TODO': 'Preparing to start',\n        'DOING': 'Working on task',\n        'DONE': 'Task completed',\n        'BLOCKED': 'Task blocked',\n        'REVISE': 'Revising work'\n    };\n    const message = statusMessages[status] || 'Processing';\n    logKaibanTeam('Task status message generated', {\n        status,\n        message\n    });\n    return message;\n}\nfunction getPhaseFromTask(task) {\n    const taskPhases = {\n        'Topic Analysis': 'topic-analysis',\n        'Content Strategy': 'content-strategy',\n        'Primary Research': 'primary-research',\n        'Gap Analysis': 'gap-analysis',\n        'Deep Research': 'deep-research',\n        'Content Generation': 'content-generation',\n        'Quality Assurance': 'quality-assurance'\n    };\n    const phase = taskPhases[task.description] || 'processing';\n    logKaibanTeam('Phase determined from task', {\n        taskDescription: task.description,\n        phase\n    });\n    return phase;\n}\nfunction extractWorkflowResults(result, team) {\n    logKaibanTeam('🔍 Starting workflow result extraction');\n    try {\n        const useStore = team.useStore();\n        const state = useStore.getState();\n        logKaibanTeam('Team state retrieved', {\n            totalTasks: state.tasks?.length || 0,\n            totalAgents: state.agents?.length || 0,\n            totalLogs: state.workflowLogs?.length || 0\n        });\n        // Extract task results\n        const taskResults = state.tasks.map((task)=>({\n                name: task.description,\n                status: task.status,\n                result: task.result\n            }));\n        logKaibanTeam('Task results extracted', {\n            totalTasks: taskResults.length,\n            taskStatuses: taskResults.map((t)=>({\n                    name: t.name,\n                    status: t.status\n                }))\n        });\n        // Extract final content from the last task (Quality Assurance)\n        const finalTask = taskResults[taskResults.length - 1];\n        const contentTask = taskResults[taskResults.length - 2]; // Content Generation task\n        logKaibanTeam('Identifying final tasks', {\n            finalTaskName: finalTask?.name,\n            finalTaskStatus: finalTask?.status,\n            contentTaskName: contentTask?.name,\n            contentTaskStatus: contentTask?.status\n        });\n        let finalContent = {};\n        let qualityMetrics = {};\n        try {\n            if (contentTask?.result) {\n                finalContent = typeof contentTask.result === 'string' ? JSON.parse(contentTask.result) : contentTask.result;\n                logKaibanTeam('Content task result parsed', {\n                    hasTitle: !!finalContent.title,\n                    hasContent: !!finalContent.content,\n                    wordCount: finalContent.wordCount || 0\n                });\n            }\n            if (finalTask?.result) {\n                qualityMetrics = typeof finalTask.result === 'string' ? JSON.parse(finalTask.result) : finalTask.result;\n                logKaibanTeam('Quality metrics parsed', {\n                    hasQualityScore: !!qualityMetrics.overallQualityScore,\n                    qualityScore: qualityMetrics.overallQualityScore || 0\n                });\n            }\n        } catch (parseError) {\n            logKaibanTeam('⚠️ Error parsing task results', {\n                error: parseError instanceof Error ? parseError.message : 'Unknown parse error',\n                contentTaskResult: contentTask?.result,\n                finalTaskResult: finalTask?.result\n            });\n        }\n        const extractedResults = {\n            topicAnalysis: taskResults[0]?.result,\n            contentStrategy: taskResults[1]?.result,\n            primaryResearch: taskResults[2]?.result,\n            gapAnalysis: taskResults[3]?.result,\n            deepResearch: taskResults[4]?.result,\n            generatedContent: finalContent,\n            qualityAssurance: qualityMetrics,\n            title: finalContent.title || 'Generated Content',\n            content: finalContent.content || '',\n            wordCount: finalContent.wordCount || 0,\n            qualityScore: qualityMetrics.overallQualityScore || 0,\n            sourcesUsed: finalContent.sourcesUsed?.length || 0,\n            agentPerformance: state.agents.map((agent)=>({\n                    name: agent.name,\n                    status: agent.status,\n                    tasksCompleted: agent.tasksCompleted || 0\n                })),\n            workflowLogs: state.workflowLogs || []\n        };\n        logKaibanTeam('✅ Workflow results extracted successfully', {\n            title: extractedResults.title,\n            contentLength: extractedResults.content?.length || 0,\n            wordCount: extractedResults.wordCount,\n            qualityScore: extractedResults.qualityScore,\n            sourcesUsed: extractedResults.sourcesUsed,\n            agentCount: extractedResults.agentPerformance?.length || 0,\n            logCount: extractedResults.workflowLogs?.length || 0,\n            allTasksCompleted: taskResults.every((t)=>t.status === 'DONE')\n        });\n        return extractedResults;\n    } catch (error) {\n        logKaibanTeam('❌ Error extracting workflow results', {\n            error: error instanceof Error ? error.message : 'Unknown error',\n            errorStack: error instanceof Error ? error.stack : undefined\n        });\n        return {\n            title: 'Content Generation Error',\n            content: 'An error occurred during content generation.',\n            wordCount: 0,\n            qualityScore: 0,\n            sourcesUsed: 0\n        };\n    }\n}\n/**\n * Export the main team creation function as default\n */ \n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban/super-agent-team.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban/tasks.ts":
/*!****************************************!*\
  !*** ./src/lib/agents/kaiban/tasks.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contentGenerationTask: () => (/* binding */ contentGenerationTask),\n/* harmony export */   contentStrategyTask: () => (/* binding */ contentStrategyTask),\n/* harmony export */   deepResearchTask: () => (/* binding */ deepResearchTask),\n/* harmony export */   gapAnalysisTask: () => (/* binding */ gapAnalysisTask),\n/* harmony export */   primaryResearchTask: () => (/* binding */ primaryResearchTask),\n/* harmony export */   qualityAssuranceTask: () => (/* binding */ qualityAssuranceTask),\n/* harmony export */   superAgentTasks: () => (/* binding */ superAgentTasks),\n/* harmony export */   taskConfig: () => (/* binding */ taskConfig),\n/* harmony export */   topicAnalysisTask: () => (/* binding */ topicAnalysisTask)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/agents/kaiban/agents.ts\");\n/**\n * KaibanJS Tasks for Super Agent Workflow\n *\n * This file defines all the tasks that agents will execute in sequence\n * to complete the super agent workflow using KaibanJS framework.\n */ \n\n// Console logging utility for Kaiban Tasks\nconst logKaibanTask = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`📋 [KAIBAN-TASK] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-TASK-DATA]:`, data);\n    }\n};\n// Log task system initialization\nlogKaibanTask('Initializing Kaiban Task System', {\n    totalTasks: 7,\n    taskSequence: [\n        'Topic Analysis',\n        'Content Strategy',\n        'Primary Research',\n        'Gap Analysis',\n        'Deep Research',\n        'Content Generation',\n        'Quality Assurance'\n    ]\n});\n/**\n * Task 1: Topic Analysis\n * Analyze the input topic and generate research strategy\n */ logKaibanTask('Creating Topic Analysis Task', {\n    taskNumber: 1,\n    taskName: 'Topic Analysis',\n    agent: 'Topic Analyzer',\n    expectedOutputs: [\n        'mainTopic',\n        'keySubtopics',\n        'primaryKeywords',\n        'researchQueries'\n    ]\n});\nconst topicAnalysisTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `As a Topic Analysis Specialist, analyze the topic \"{topic}\" using advanced reasoning and systematic methodology.\n\n**REASONING APPROACH**: Use chain-of-thought reasoning to break down this complex analysis task:\n\n**Step 1: Topic Deconstruction**\n- Identify the core topic and its fundamental components\n- Map out primary and secondary subtopics\n- Determine topic scope and boundaries\n- Assess topic complexity and depth requirements\n\n**Step 2: Keyword Strategy Development**\n- Generate 8-12 primary keywords with high search intent\n- Identify 15-20 semantic keywords and LSI terms\n- Create keyword clusters for content organization\n- Analyze keyword difficulty and search volume potential\n\n**Step 3: Research Query Formulation**\n- Develop 6-10 targeted search queries using advanced search operators\n- Create queries for different research phases (broad, specific, expert-level)\n- Include queries for recent developments and trends (2024-2025)\n- Design queries for finding authoritative sources and case studies\n\n**Step 4: Content Angle Analysis**\n- Identify 4-6 unique content perspectives\n- Analyze competitor content gaps and opportunities\n- Determine trending angles and emerging viewpoints\n- Assess content differentiation potential\n\n**Step 5: Audience Profiling**\n- Define primary audience demographics, psychographics, and pain points\n- Identify secondary audience segments and their specific needs\n- Determine audience knowledge level and content consumption preferences\n- Map audience journey and content touchpoints\n\n**Step 6: Competitive Intelligence**\n- Analyze existing content landscape and saturation level\n- Identify content gaps and underserved areas\n- Assess competitor strengths and weaknesses\n- Determine content differentiation opportunities\n\n**CRITICAL SUCCESS FACTORS**:\n- Ensure all analysis is data-driven and evidence-based\n- Focus on actionable insights for subsequent research phases\n- Maintain consistency with user's specified content type and target audience\n- Prioritize 2025 trends and forward-looking perspectives\n\n**OUTPUT REQUIREMENTS**: Structure your response as valid JSON following this exact schema:`,\n    expectedOutput: `A comprehensive topic analysis in JSON format with this exact structure:\n{\n  \"mainTopic\": \"string - core topic clearly defined\",\n  \"topicScope\": \"string - boundaries and focus areas\",\n  \"keySubtopics\": [\"array of 5-8 primary subtopics\"],\n  \"primaryKeywords\": [\"array of 8-12 high-intent keywords\"],\n  \"semanticKeywords\": [\"array of 15-20 LSI and semantic terms\"],\n  \"keywordClusters\": {\n    \"cluster1\": [\"related keywords\"],\n    \"cluster2\": [\"related keywords\"]\n  },\n  \"researchQueries\": [\n    {\n      \"query\": \"search query string\",\n      \"purpose\": \"broad/specific/expert/trends\",\n      \"expectedSources\": \"type of sources to find\"\n    }\n  ],\n  \"contentAngles\": [\n    {\n      \"angle\": \"perspective name\",\n      \"description\": \"detailed explanation\",\n      \"uniqueness\": \"differentiation factor\",\n      \"targetSegment\": \"audience segment\"\n    }\n  ],\n  \"targetAudience\": {\n    \"primary\": {\n      \"demographics\": \"age, profession, location\",\n      \"psychographics\": \"interests, values, motivations\",\n      \"painPoints\": [\"specific challenges\"],\n      \"knowledgeLevel\": \"beginner/intermediate/advanced\",\n      \"contentPreferences\": \"format and style preferences\"\n    },\n    \"secondary\": {\n      \"demographics\": \"secondary audience details\",\n      \"specificNeeds\": \"unique requirements\"\n    }\n  },\n  \"contentComplexity\": \"beginner/intermediate/advanced with justification\",\n  \"competitiveAnalysis\": {\n    \"contentSaturation\": \"high/medium/low\",\n    \"identifiedGaps\": [\"specific content gaps\"],\n    \"competitorStrengths\": [\"what competitors do well\"],\n    \"differentiationOpportunities\": [\"how to stand out\"]\n  },\n  \"trendAnalysis\": {\n    \"currentTrends\": [\"2024-2025 relevant trends\"],\n    \"emergingTopics\": [\"upcoming areas of interest\"],\n    \"seasonalFactors\": \"timing considerations\"\n  },\n  \"recommendedApproach\": \"strategic recommendation for content creation\",\n  \"successMetrics\": [\"how to measure content success\"],\n  \"confidenceScore\": \"1-10 rating of analysis confidence\"\n}`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.topicAnalysisAgent\n});\nlogKaibanTask('Topic Analysis Task created successfully');\n/**\n * Task 2: Content Strategy Development\n * Develop comprehensive content strategy based on topic analysis\n */ logKaibanTask('Creating Content Strategy Task', {\n    taskNumber: 2,\n    taskName: 'Content Strategy',\n    agent: 'Content Strategist',\n    dependsOn: 'Topic Analysis Task',\n    expectedOutputs: [\n        'contentStructure',\n        'keyMessages',\n        'seoStrategy'\n    ]\n});\nconst contentStrategyTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `As a Content Strategy Director, develop a comprehensive content strategy using the topic analysis: {taskResult:task1}\n\n**STRATEGIC FRAMEWORK**: Apply systematic content strategy methodology:\n\n**Step 1: Strategy Foundation Analysis**\n- Analyze topic analysis insights and competitive landscape\n- Align strategy with content specifications:\n  * Content Type: {contentType}\n  * Target Word Count: {targetWordCount}\n  * Tone: {tone}\n  * Target Audience: {targetAudience}\n- Identify primary content objectives and success metrics\n\n**Step 2: Content Architecture Design**\n- Create hierarchical content structure with logical flow\n- Design section-by-section outline with word count allocation\n- Plan content depth and detail level for each section\n- Ensure structure supports target word count and reading experience\n\n**Step 3: Message Strategy Development**\n- Craft 4-6 core messages that resonate with target audience\n- Develop supporting arguments and evidence requirements\n- Create message hierarchy and emphasis points\n- Align messages with audience pain points and interests\n\n**Step 4: Engagement Strategy Planning**\n- Design attention-grabbing hooks and opening strategies\n- Plan storytelling elements, examples, and case studies\n- Identify opportunities for interactive elements and visuals\n- Create reader retention and engagement touchpoints\n\n**Step 5: SEO Integration Strategy**\n- Develop keyword integration plan using topic analysis keywords\n- Plan natural keyword placement and density\n- Design meta elements and SEO-friendly structure\n- Create internal linking and content optimization strategy\n\n**Step 6: Differentiation and Positioning**\n- Leverage competitive analysis to identify unique positioning\n- Plan content elements that differentiate from competitors\n- Design value propositions and unique insights\n- Create memorable and shareable content elements\n\n**QUALITY STANDARDS**:\n- Ensure strategy aligns with 2025 content trends and best practices\n- Focus on reader value and actionable insights\n- Maintain consistency with specified tone and audience preferences\n- Plan for measurable engagement and conversion outcomes\n\n**OUTPUT REQUIREMENTS**: Provide detailed strategy in valid JSON format:`,\n    expectedOutput: `A comprehensive content strategy in JSON format with this exact structure:\n{\n  \"strategyOverview\": {\n    \"primaryObjective\": \"main content goal\",\n    \"secondaryObjectives\": [\"supporting goals\"],\n    \"targetMetrics\": [\"measurable success indicators\"],\n    \"contentPillars\": [\"3-4 main content themes\"]\n  },\n  \"contentStructure\": {\n    \"introduction\": {\n      \"wordCount\": \"number\",\n      \"keyElements\": [\"hook\", \"preview\", \"value proposition\"],\n      \"estimatedReadTime\": \"minutes\"\n    },\n    \"mainSections\": [\n      {\n        \"sectionTitle\": \"section name\",\n        \"wordCount\": \"allocated words\",\n        \"keyPoints\": [\"main points to cover\"],\n        \"supportingElements\": [\"examples\", \"data\", \"quotes\"],\n        \"transitionStrategy\": \"how to connect to next section\"\n      }\n    ],\n    \"conclusion\": {\n      \"wordCount\": \"number\",\n      \"keyElements\": [\"summary\", \"call-to-action\", \"next steps\"],\n      \"memorableClosing\": \"final impression strategy\"\n    }\n  },\n  \"keyMessages\": [\n    {\n      \"message\": \"core message text\",\n      \"priority\": \"high/medium/low\",\n      \"placement\": \"where in content\",\n      \"supportingEvidence\": \"type of proof needed\",\n      \"audienceResonance\": \"why it matters to readers\"\n    }\n  ],\n  \"contentFlow\": {\n    \"narrativeArc\": \"overall story progression\",\n    \"logicalProgression\": [\"step-by-step flow\"],\n    \"transitionStrategies\": [\"how sections connect\"],\n    \"paceAndRhythm\": \"content pacing strategy\"\n  },\n  \"engagementElements\": {\n    \"openingHooks\": [\"attention-grabbing techniques\"],\n    \"storytellingElements\": [\"narratives\", \"case studies\", \"examples\"],\n    \"interactiveElements\": [\"questions\", \"exercises\", \"tools\"],\n    \"visualElements\": [\"charts\", \"infographics\", \"images\"],\n    \"retentionTactics\": [\"techniques to keep readers engaged\"]\n  },\n  \"seoStrategy\": {\n    \"primaryKeywords\": [\"main SEO targets with placement\"],\n    \"secondaryKeywords\": [\"supporting terms with usage\"],\n    \"keywordDensity\": \"target percentage\",\n    \"headingStrategy\": \"H1, H2, H3 keyword integration\",\n    \"metaElements\": {\n      \"title\": \"SEO-optimized title\",\n      \"description\": \"meta description\",\n      \"focusKeyphrase\": \"primary SEO target\"\n    },\n    \"internalLinking\": \"linking strategy\"\n  },\n  \"differentiationStrategy\": {\n    \"uniqueAngle\": \"what makes this content different\",\n    \"competitiveAdvantages\": [\"how we beat competitors\"],\n    \"valueProposition\": \"unique value for readers\",\n    \"memorabilityFactors\": [\"what makes it stick\"]\n  },\n  \"callToAction\": {\n    \"primary\": \"main action for readers\",\n    \"secondary\": [\"alternative actions\"],\n    \"placement\": \"where CTAs appear\",\n    \"persuasionTechniques\": [\"psychological triggers\"]\n  },\n  \"qualityAssurance\": {\n    \"readabilityTarget\": \"grade level/score\",\n    \"toneConsistency\": \"tone maintenance strategy\",\n    \"factCheckingNeeds\": [\"areas requiring verification\"],\n    \"reviewCriteria\": [\"quality checkpoints\"]\n  },\n  \"estimatedMetrics\": {\n    \"readingTime\": \"minutes\",\n    \"engagementScore\": \"predicted 1-10\",\n    \"shareabilityFactor\": \"viral potential 1-10\",\n    \"conversionPotential\": \"action likelihood 1-10\"\n  }\n}`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.contentStrategyAgent\n});\nlogKaibanTask('Content Strategy Task created successfully');\n/**\n * Task 3: Primary Research\n * Conduct initial research using the research queries from topic analysis\n */ logKaibanTask('Creating Primary Research Task', {\n    taskNumber: 3,\n    taskName: 'Primary Research',\n    agent: 'Primary Researcher',\n    dependsOn: 'Topic Analysis Task',\n    expectedOutputs: [\n        'researchData',\n        'keyFindings',\n        'statisticsAndData'\n    ]\n});\nconst primaryResearchTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `As a Primary Research Specialist, conduct systematic research for \"{topic}\" using the strategic framework from: {taskResult:task1}\n\n**RESEARCH METHODOLOGY**: Execute comprehensive research using proven academic and industry standards:\n\n**Phase 1: Research Planning and Query Optimization**\n- Analyze research queries from topic analysis for maximum effectiveness\n- Optimize search queries using advanced Google search operators\n- Plan research sequence from broad to specific information gathering\n- Establish source quality criteria and evaluation framework\n\n**Phase 2: Systematic Google Search Execution**\n- Execute each research query using Google Programmable Search Engine API\n- Apply advanced search operators for precision: site:, filetype:, intitle:, inurl:\n- Search for recent content (2024-2025) using date filters\n- Target authoritative domains: .edu, .gov, industry leaders, expert publications\n- Maximum results per query: {maxPrimaryResults}\n\n**Phase 3: Source Evaluation and Credibility Assessment**\n- Evaluate each source using academic credibility criteria:\n  * Author expertise and credentials\n  * Publication reputation and authority\n  * Content accuracy and fact-checking\n  * Recency and relevance to topic\n  * Citation quality and references\n- Rate sources on 1-10 reliability scale with detailed justification\n- Prioritize peer-reviewed, expert-authored, and authoritative sources\n\n**Phase 4: Content Extraction and Data Mining**\n- Extract key information, statistics, and insights from each source\n- Identify expert quotes and authoritative statements\n- Collect case studies, examples, and real-world applications\n- Gather supporting data, charts, and quantitative evidence\n- Document methodology and research limitations where applicable\n\n**Phase 5: Data Organization and Synthesis**\n- Organize findings by topic relevance and content strategy alignment\n- Create thematic clusters of related information\n- Identify patterns, trends, and consensus across sources\n- Flag conflicting information for further investigation\n- Prepare data for seamless integration with content strategy\n\n**RESEARCH QUALITY STANDARDS**:\n- Prioritize authoritative and expert sources over general content\n- Ensure geographic and demographic diversity in sources when relevant\n- Focus on actionable insights and practical applications\n- Maintain rigorous fact-checking and source verification\n- Document research limitations and potential biases\n\n**CRITICAL SUCCESS FACTORS**:\n- Align all research with content strategy requirements from Task 2\n- Focus on information that supports key messages and content structure\n- Prioritize recent developments and 2025 trends\n- Ensure research depth matches target audience knowledge level\n\n**OUTPUT REQUIREMENTS**: Compile comprehensive research in structured JSON format:`,\n    expectedOutput: `Comprehensive primary research data in JSON format with this exact structure:\n{\n  \"researchSummary\": {\n    \"totalQueries\": \"number of search queries executed\",\n    \"totalSources\": \"number of sources evaluated\",\n    \"researchScope\": \"breadth and depth of research conducted\",\n    \"keyThemes\": [\"main themes discovered\"],\n    \"researchLimitations\": [\"any limitations or gaps identified\"]\n  },\n  \"researchData\": [\n    {\n      \"title\": \"source title\",\n      \"url\": \"source URL\",\n      \"author\": \"author name and credentials\",\n      \"publication\": \"publication name and type\",\n      \"publishDate\": \"publication date\",\n      \"sourceType\": \"academic/industry/expert/news/government\",\n      \"reliabilityScore\": \"1-10 with justification\",\n      \"relevanceScore\": \"1-10 with justification\",\n      \"keyContent\": \"extracted key information\",\n      \"statistics\": [\"numerical data and metrics\"],\n      \"quotes\": [\"expert quotes and statements\"],\n      \"methodology\": \"research methodology if applicable\",\n      \"limitations\": \"study limitations if applicable\"\n    }\n  ],\n  \"synthesizedFindings\": {\n    \"keyInsights\": [\n      {\n        \"insight\": \"major finding or insight\",\n        \"supportingSources\": [\"URLs of supporting sources\"],\n        \"confidence\": \"high/medium/low\",\n        \"implications\": \"what this means for content\"\n      }\n    ],\n    \"statisticalData\": [\n      {\n        \"statistic\": \"numerical finding\",\n        \"source\": \"source attribution\",\n        \"context\": \"context and significance\",\n        \"reliability\": \"assessment of data quality\"\n      }\n    ],\n    \"expertPerspectives\": [\n      {\n        \"expert\": \"expert name and credentials\",\n        \"perspective\": \"expert viewpoint or quote\",\n        \"source\": \"source attribution\",\n        \"relevance\": \"how it supports content strategy\"\n      }\n    ],\n    \"caseStudies\": [\n      {\n        \"title\": \"case study title\",\n        \"summary\": \"brief description\",\n        \"source\": \"source attribution\",\n        \"keyLessons\": [\"main takeaways\"],\n        \"applicability\": \"relevance to topic\"\n      }\n    ]\n  },\n  \"researchGaps\": {\n    \"identifiedGaps\": [\"areas needing more research\"],\n    \"conflictingInformation\": [\"areas with contradictory data\"],\n    \"missingPerspectives\": [\"viewpoints not yet covered\"],\n    \"dataLimitations\": [\"limitations in available data\"]\n  },\n  \"sourceQuality\": {\n    \"highQualitySources\": \"number of sources rated 8-10\",\n    \"mediumQualitySources\": \"number of sources rated 5-7\",\n    \"lowQualitySources\": \"number of sources rated 1-4\",\n    \"averageReliability\": \"average reliability score\",\n    \"sourceDistribution\": {\n      \"academic\": \"percentage\",\n      \"industry\": \"percentage\",\n      \"expert\": \"percentage\",\n      \"government\": \"percentage\",\n      \"other\": \"percentage\"\n    }\n  },\n  \"researchMetrics\": {\n    \"totalSearchTime\": \"estimated time spent\",\n    \"sourcesPerQuery\": \"average sources per query\",\n    \"informationDensity\": \"quality of information gathered\",\n    \"researchCompleteness\": \"percentage of research objectives met\"\n  }\n}`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.primaryResearchAgent\n});\nlogKaibanTask('Primary Research Task created successfully');\n/**\n * Task 4: Gap Analysis\n * Identify gaps in the primary research data\n */ logKaibanTask('Creating Gap Analysis Task', {\n    taskNumber: 4,\n    taskName: 'Gap Analysis',\n    agent: 'Gap Analyst',\n    dependsOn: [\n        'Primary Research Task',\n        'Content Strategy Task'\n    ],\n    expectedOutputs: [\n        'gapsSummary',\n        'totalGapsIdentified',\n        'prioritizedResearchQueries'\n    ]\n});\nconst gapAnalysisTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `As a Research Gap Analyst, conduct systematic gap analysis using advanced reasoning to compare:\n- Primary Research Data: {taskResult:task3}\n- Content Strategy Requirements: {taskResult:task2}\n\n**ANALYTICAL FRAMEWORK**: Apply rigorous gap analysis methodology using chain-of-thought reasoning:\n\n**Step 1: Content Strategy Alignment Assessment**\n- Map primary research findings to content strategy requirements\n- Identify content structure sections lacking sufficient research support\n- Assess alignment between key messages and available evidence\n- Evaluate research coverage of target audience needs and pain points\n\n**Step 2: Information Completeness Analysis**\n- Analyze information density and depth for each content section\n- Identify missing statistics, data points, and quantitative evidence\n- Assess coverage of key subtopics and content angles\n- Evaluate balance between different types of information (theoretical vs. practical)\n\n**Step 3: Source Authority and Credibility Evaluation**\n- Assess authority level of current sources against content requirements\n- Identify areas needing more expert perspectives or authoritative sources\n- Evaluate geographic, demographic, and industry representation\n- Assess need for peer-reviewed or academic sources\n\n**Step 4: Perspective and Viewpoint Analysis**\n- Identify underrepresented viewpoints or expert opinions\n- Assess balance of perspectives (pro/con, different schools of thought)\n- Evaluate coverage of diverse demographics and use cases\n- Identify missing stakeholder perspectives\n\n**Step 5: Evidence and Support Material Assessment**\n- Evaluate sufficiency of supporting evidence for key claims\n- Identify areas lacking case studies, examples, or real-world applications\n- Assess need for additional proof points and validation\n- Evaluate balance between anecdotal and empirical evidence\n\n**Step 6: Currency and Trend Analysis**\n- Assess recency of information against 2025 content standards\n- Identify areas needing current trends and developments\n- Evaluate coverage of emerging topics and future predictions\n- Assess need for recent case studies and contemporary examples\n\n**Step 7: Practical Application and Actionability Gaps**\n- Identify missing practical applications and implementation guidance\n- Assess coverage of tools, techniques, and methodologies\n- Evaluate presence of actionable insights and next steps\n- Identify gaps in how-to information and practical examples\n\n**CRITICAL ANALYSIS STANDARDS**:\n- Use systematic comparison methodology for objective assessment\n- Prioritize gaps based on content strategy impact and audience value\n- Focus on actionable gaps that can be addressed through targeted research\n- Consider research feasibility and source availability\n\n**OUTPUT REQUIREMENTS**: Provide comprehensive gap analysis in structured JSON format:`,\n    expectedOutput: `Comprehensive gap analysis report in JSON format with this exact structure:\n{\n  \"executiveSummary\": {\n    \"totalGapsIdentified\": \"number\",\n    \"criticalGaps\": \"number of high-priority gaps\",\n    \"overallCompleteness\": \"percentage of content strategy covered\",\n    \"researchPriority\": \"high/medium/low urgency for additional research\",\n    \"keyRecommendations\": [\"top 3-5 recommendations\"]\n  },\n  \"contentAlignmentAnalysis\": {\n    \"strategyCoverage\": \"percentage of strategy requirements met\",\n    \"wellCoveredAreas\": [\"areas with sufficient research\"],\n    \"underCoveredAreas\": [\"areas needing more research\"],\n    \"missingElements\": [\"completely missing components\"]\n  },\n  \"gapCategories\": {\n    \"informationGaps\": [\n      {\n        \"gapDescription\": \"specific information missing\",\n        \"importance\": \"1-10 scale with justification\",\n        \"impactOnContent\": \"how this affects content quality\",\n        \"targetedQueries\": [\"specific search queries to address gap\"],\n        \"recommendedSources\": [\"types of sources needed\"],\n        \"priority\": \"high/medium/low\",\n        \"estimatedResearchTime\": \"time needed to fill gap\"\n      }\n    ],\n    \"perspectiveGaps\": [\n      {\n        \"missingPerspective\": \"viewpoint or expert opinion needed\",\n        \"importance\": \"1-10 scale with justification\",\n        \"stakeholderType\": \"type of expert or perspective needed\",\n        \"targetedQueries\": [\"queries to find these perspectives\"],\n        \"priority\": \"high/medium/low\"\n      }\n    ],\n    \"evidenceGaps\": [\n      {\n        \"evidenceType\": \"type of evidence missing\",\n        \"claimsNeedingSupport\": [\"specific claims requiring evidence\"],\n        \"importance\": \"1-10 scale\",\n        \"targetedQueries\": [\"queries to find supporting evidence\"],\n        \"priority\": \"high/medium/low\"\n      }\n    ],\n    \"depthGaps\": [\n      {\n        \"topicArea\": \"area needing deeper exploration\",\n        \"currentDepth\": \"superficial/moderate/detailed\",\n        \"requiredDepth\": \"level needed for content strategy\",\n        \"importance\": \"1-10 scale\",\n        \"targetedQueries\": [\"queries for deeper research\"],\n        \"priority\": \"high/medium/low\"\n      }\n    ],\n    \"currencyGaps\": [\n      {\n        \"outdatedArea\": \"area with outdated information\",\n        \"latestDataDate\": \"most recent data found\",\n        \"requiredRecency\": \"how recent information needs to be\",\n        \"importance\": \"1-10 scale\",\n        \"targetedQueries\": [\"queries for recent information\"],\n        \"priority\": \"high/medium/low\"\n      }\n    ],\n    \"authorityGaps\": [\n      {\n        \"topicArea\": \"area needing more authoritative sources\",\n        \"currentAuthorityLevel\": \"assessment of current sources\",\n        \"requiredAuthorityLevel\": \"level needed\",\n        \"importance\": \"1-10 scale\",\n        \"targetedQueries\": [\"queries for authoritative sources\"],\n        \"priority\": \"high/medium/low\"\n      }\n    ],\n    \"practicalGaps\": [\n      {\n        \"practicalArea\": \"area missing practical application\",\n        \"theoreticalCoverage\": \"level of theoretical information\",\n        \"practicalNeed\": \"type of practical information needed\",\n        \"importance\": \"1-10 scale\",\n        \"targetedQueries\": [\"queries for practical examples\"],\n        \"priority\": \"high/medium/low\"\n      }\n    ]\n  },\n  \"prioritizedResearchPlan\": {\n    \"highPriorityGaps\": [\n      {\n        \"gap\": \"gap description\",\n        \"rationale\": \"why this is high priority\",\n        \"researchQueries\": [\"specific queries\"],\n        \"expectedOutcome\": \"what this research will achieve\",\n        \"timeEstimate\": \"estimated research time\"\n      }\n    ],\n    \"mediumPriorityGaps\": [\"similar structure for medium priority\"],\n    \"lowPriorityGaps\": [\"similar structure for low priority\"]\n  },\n  \"researchRecommendations\": {\n    \"immediateActions\": [\"actions to take first\"],\n    \"researchSequence\": [\"recommended order of research\"],\n    \"sourceTargets\": [\"specific types of sources to prioritize\"],\n    \"searchStrategies\": [\"recommended search approaches\"],\n    \"qualityThresholds\": [\"minimum quality standards for new sources\"]\n  },\n  \"riskAssessment\": {\n    \"contentQualityRisks\": [\"risks if gaps not addressed\"],\n    \"audienceImpact\": [\"how gaps affect audience value\"],\n    \"competitiveRisks\": [\"risks compared to competitor content\"],\n    \"mitigationStrategies\": [\"how to minimize risks\"]\n  },\n  \"successMetrics\": {\n    \"completenessTarget\": \"target percentage after gap filling\",\n    \"qualityImprovement\": \"expected quality enhancement\",\n    \"researchROI\": \"value of additional research effort\"\n  }\n}`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.gapAnalysisAgent\n});\nlogKaibanTask('Gap Analysis Task created successfully');\n/**\n * Task 5: Deep Research\n * Conduct targeted research to fill identified gaps\n */ logKaibanTask('Creating Deep Research Task', {\n    taskNumber: 5,\n    taskName: 'Deep Research',\n    agent: 'Deep Researcher',\n    dependsOn: 'Gap Analysis Task',\n    expectedOutputs: [\n        'deepResearchSummary',\n        'gapsAddressed',\n        'integratedFindings'\n    ]\n});\nconst deepResearchTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `As a Deep Research Specialist, execute targeted research to address priority gaps identified in: {taskResult:task4}\n\n**DEEP RESEARCH METHODOLOGY**: Apply advanced research techniques with systematic gap-filling approach:\n\n**Phase 1: Gap Prioritization and Research Planning**\n- Analyze high and medium priority gaps from gap analysis\n- Develop specialized search strategies for each gap category\n- Plan research sequence to maximize efficiency and source quality\n- Establish success criteria for gap resolution\n\n**Phase 2: Advanced Google Search Execution**\n- Execute targeted searches using Google Programmable Search Engine API\n- Apply sophisticated search operators and techniques:\n  * Boolean operators (AND, OR, NOT) for precision\n  * Site-specific searches (site:edu, site:gov, site:expert-domains)\n  * File type searches (filetype:pdf for research papers)\n  * Date range filters for recent information (2024-2025)\n  * Exact phrase searches for specific concepts\n- Maximum results per gap: {maxDeepResults}\n- Focus on authoritative and expert sources\n\n**Phase 3: Expert Perspective and Authority Research**\n- Target thought leaders, industry experts, and academic authorities\n- Search for expert interviews, opinions, and commentary\n- Locate authoritative publications and peer-reviewed sources\n- Find expert social media insights and professional commentary\n- Identify conference presentations and expert talks\n\n**Phase 4: Evidence and Case Study Research**\n- Search for supporting case studies and real-world examples\n- Locate empirical evidence and research studies\n- Find practical applications and implementation examples\n- Gather success stories and failure analyses\n- Collect quantitative data and statistical evidence\n\n**Phase 5: Currency and Trend Research**\n- Focus on 2024-2025 developments and emerging trends\n- Search for recent news, updates, and industry developments\n- Locate forward-looking predictions and trend analyses\n- Find recent regulatory changes and industry shifts\n- Identify emerging technologies and methodologies\n\n**Phase 6: Cross-Validation and Verification**\n- Cross-reference findings across multiple authoritative sources\n- Validate claims and statistics through primary sources\n- Identify consensus and conflicting viewpoints\n- Assess information reliability and credibility\n- Document source quality and verification status\n\n**RESEARCH QUALITY STANDARDS**:\n- Prioritize peer-reviewed and academically rigorous sources\n- Focus on expert-authored content and authoritative publications\n- Ensure geographic and demographic diversity when relevant\n- Maintain high standards for source credibility and reliability\n- Document research methodology and limitations\n\n**INTEGRATION REQUIREMENTS**:\n- Seamlessly integrate findings with existing primary research\n- Resolve conflicts between primary and deep research findings\n- Create comprehensive knowledge base for content generation\n- Ensure all gaps are adequately addressed or documented as limitations\n\n**OUTPUT REQUIREMENTS**: Provide comprehensive deep research results in structured JSON format:`,\n    expectedOutput: `Comprehensive deep research results in JSON format with this exact structure:\n{\n  \"researchSummary\": {\n    \"totalGapsTargeted\": \"number of gaps addressed\",\n    \"gapsFullyResolved\": \"number completely filled\",\n    \"gapsPartiallyResolved\": \"number partially addressed\",\n    \"newSourcesFound\": \"total new sources discovered\",\n    \"researchDepth\": \"assessment of research thoroughness\",\n    \"overallSuccess\": \"percentage of research objectives met\"\n  },\n  \"gapResolution\": [\n    {\n      \"gapId\": \"reference to original gap\",\n      \"gapDescription\": \"what gap was being addressed\",\n      \"resolutionStatus\": \"fully/partially/not resolved\",\n      \"newInformation\": \"key information found\",\n      \"sources\": [\"URLs and citations\"],\n      \"qualityAssessment\": \"assessment of information quality\",\n      \"remainingLimitations\": \"any remaining gaps or limitations\"\n    }\n  ],\n  \"newResearchData\": [\n    {\n      \"title\": \"source title\",\n      \"url\": \"source URL\",\n      \"author\": \"author and credentials\",\n      \"publication\": \"publication details\",\n      \"publishDate\": \"publication date\",\n      \"sourceType\": \"academic/expert/industry/government\",\n      \"reliabilityScore\": \"1-10 with justification\",\n      \"relevanceScore\": \"1-10 with justification\",\n      \"gapAddressed\": \"which gap this source addresses\",\n      \"keyContent\": \"extracted key information\",\n      \"uniqueInsights\": \"new insights not found in primary research\",\n      \"expertCredentials\": \"author expertise and authority\"\n    }\n  ],\n  \"expertInsights\": [\n    {\n      \"expert\": \"expert name and credentials\",\n      \"expertise\": \"area of specialization\",\n      \"insight\": \"expert perspective or quote\",\n      \"source\": \"where insight was found\",\n      \"credibility\": \"assessment of expert authority\",\n      \"relevance\": \"how insight addresses content needs\",\n      \"uniqueness\": \"what makes this perspective valuable\"\n    }\n  ],\n  \"evidenceAndCaseStudies\": [\n    {\n      \"type\": \"case study/research study/example\",\n      \"title\": \"study or example title\",\n      \"summary\": \"brief description\",\n      \"keyFindings\": [\"main conclusions\"],\n      \"methodology\": \"research approach if applicable\",\n      \"source\": \"source attribution\",\n      \"relevance\": \"how it supports content strategy\",\n      \"credibility\": \"assessment of evidence quality\"\n    }\n  ],\n  \"currentTrends\": [\n    {\n      \"trend\": \"trend description\",\n      \"timeframe\": \"when trend is occurring\",\n      \"source\": \"source of trend information\",\n      \"implications\": \"what this means for the topic\",\n      \"evidence\": \"supporting data or examples\",\n      \"futureProjections\": \"where trend is heading\"\n    }\n  ],\n  \"integratedFindings\": {\n    \"combinedDataset\": \"summary of primary + deep research\",\n    \"resolvedConflicts\": [\"conflicts resolved between sources\"],\n    \"strengthenedAreas\": [\"areas where deep research reinforced primary findings\"],\n    \"newDiscoveries\": [\"completely new information found\"],\n    \"validatedClaims\": [\"claims confirmed through multiple sources\"],\n    \"remainingUncertainties\": [\"areas still needing clarification\"]\n  },\n  \"researchQuality\": {\n    \"sourceDistribution\": {\n      \"academic\": \"percentage\",\n      \"expert\": \"percentage\",\n      \"industry\": \"percentage\",\n      \"government\": \"percentage\"\n    },\n    \"averageReliability\": \"average reliability score\",\n    \"informationRecency\": \"average age of information\",\n    \"expertAuthorityLevel\": \"assessment of expert source quality\",\n    \"crossValidation\": \"percentage of claims verified by multiple sources\"\n  },\n  \"contentReadiness\": {\n    \"researchCompleteness\": \"percentage of content needs met\",\n    \"informationDensity\": \"richness of available information\",\n    \"perspectiveDiversity\": \"range of viewpoints covered\",\n    \"evidenceStrength\": \"quality of supporting evidence\",\n    \"practicalApplicability\": \"availability of practical examples\",\n    \"readinessForGeneration\": \"high/medium/low readiness for content creation\"\n  },\n  \"researchLimitations\": {\n    \"unresolvedGaps\": [\"gaps that couldn't be filled\"],\n    \"sourceConstraints\": [\"limitations in available sources\"],\n    \"accessRestrictions\": [\"information behind paywalls or restricted\"],\n    \"conflictingInformation\": [\"areas with contradictory data\"],\n    \"recommendedFutureResearch\": [\"suggestions for additional research\"]\n  }\n}`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.deepResearchAgent\n});\nlogKaibanTask('Deep Research Task created successfully');\n/**\n * Task 6: Content Generation\n * Generate the final content based on all research and strategy\n */ logKaibanTask('Creating Content Generation Task', {\n    taskNumber: 6,\n    taskName: 'Content Generation',\n    agent: 'Content Generator',\n    dependsOn: [\n        'Content Strategy Task',\n        'Primary Research Task',\n        'Deep Research Task'\n    ],\n    expectedOutputs: [\n        'title',\n        'content',\n        'wordCount',\n        'sourcesUsed'\n    ]\n});\nconst contentGenerationTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `As a Senior Content Creator, generate exceptional content for \"{topic}\" using comprehensive research and strategic framework:\n\n**CONTENT CREATION FRAMEWORK**: Apply systematic content generation methodology:\n\n**Input Analysis and Integration**:\n- Content Strategy Framework: {taskResult:task2}\n- Primary Research Foundation: {taskResult:task3}\n- Deep Research Enhancement: {taskResult:task5}\n\n**Content Specifications**:\n- Content Type: {contentType}\n- Target Word Count: {targetWordCount} (strict adherence required)\n- Tone: {tone}\n- Target Audience: {targetAudience}\n\n**Phase 1: Content Architecture Implementation**\n- Follow the exact content structure from the strategy framework\n- Allocate word count precisely according to section planning\n- Implement the narrative arc and logical progression\n- Ensure seamless transitions between sections\n\n**Phase 2: Research Integration and Evidence Weaving**\n- Seamlessly integrate findings from both primary and deep research\n- Incorporate statistics, expert quotes, and case studies naturally\n- Balance theoretical insights with practical applications\n- Ensure all claims are supported by credible sources\n\n**Phase 3: Engagement and Value Creation**\n- Implement planned hooks and attention-grabbing elements\n- Weave in storytelling elements and real-world examples\n- Create actionable insights and practical takeaways\n- Design memorable and shareable content moments\n\n**Phase 4: SEO and Discoverability Optimization**\n- Integrate primary and secondary keywords naturally\n- Optimize headings and subheadings for search engines\n- Create compelling meta elements and descriptions\n- Ensure content structure supports SEO objectives\n\n**Phase 5: Quality and Readability Enhancement**\n- Maintain consistent tone throughout the content\n- Ensure appropriate reading level for target audience\n- Create smooth flow and logical progression\n- Implement engagement retention techniques\n\n**Phase 6: Citation and Attribution**\n- Properly attribute all sources and research findings\n- Create comprehensive source list with proper formatting\n- Ensure credibility through transparent sourcing\n- Balance authority with readability\n\n**CONTENT QUALITY STANDARDS**:\n- Strictly adhere to target word count (no trimming afterward)\n- Maintain exceptional readability and flow\n- Ensure every paragraph adds unique value\n- Create content that stands out from competitors\n- Focus on 2025 trends and forward-looking perspectives\n- Include tables and visual elements when beneficial\n\n**CRITICAL SUCCESS FACTORS**:\n- Content must be publication-ready without further editing\n- All research must be seamlessly integrated, not just appended\n- Tone must remain consistent with specifications throughout\n- Word count must be precisely met during generation\n- Content must provide exceptional value to target audience\n\n**OUTPUT REQUIREMENTS**: Generate complete, publication-ready content in structured JSON format:`,\n    expectedOutput: `Complete content package in JSON format with this exact structure:\n{\n  \"contentMetadata\": {\n    \"title\": \"engaging, SEO-optimized title\",\n    \"subtitle\": \"compelling subtitle if applicable\",\n    \"metaDescription\": \"SEO meta description (150-160 characters)\",\n    \"focusKeyphrase\": \"primary SEO target keyword\",\n    \"wordCount\": \"exact word count achieved\",\n    \"estimatedReadingTime\": \"minutes\",\n    \"contentType\": \"article/guide/analysis/etc\",\n    \"targetAudience\": \"primary audience description\",\n    \"tone\": \"tone maintained throughout\"\n  },\n  \"content\": \"FULL CONTENT IN MARKDOWN FORMAT - This should be the complete, publication-ready article with proper markdown formatting, headings, and structure\",\n  \"contentStructure\": {\n    \"outline\": [\n      {\n        \"section\": \"section title\",\n        \"level\": \"H1/H2/H3\",\n        \"wordCount\": \"words in this section\",\n        \"keyPoints\": [\"main points covered\"]\n      }\n    ],\n    \"introduction\": {\n      \"hook\": \"opening hook used\",\n      \"valueProposition\": \"what readers will gain\",\n      \"roadmap\": \"content preview\"\n    },\n    \"mainSections\": [\n      {\n        \"title\": \"section title\",\n        \"wordCount\": \"section word count\",\n        \"keyMessages\": [\"messages conveyed\"],\n        \"researchIntegrated\": [\"research sources used\"],\n        \"engagementElements\": [\"hooks, examples, stories used\"]\n      }\n    ],\n    \"conclusion\": {\n      \"summary\": \"key points summarized\",\n      \"callToAction\": \"action for readers\",\n      \"memorableClosing\": \"final impression\"\n    }\n  },\n  \"researchIntegration\": {\n    \"sourcesUsed\": [\n      {\n        \"source\": \"source title and URL\",\n        \"type\": \"statistic/quote/case study/example\",\n        \"placement\": \"where used in content\",\n        \"purpose\": \"why included\",\n        \"credibility\": \"source authority level\"\n      }\n    ],\n    \"statisticsIncluded\": [\n      {\n        \"statistic\": \"numerical data\",\n        \"source\": \"attribution\",\n        \"context\": \"how presented in content\"\n      }\n    ],\n    \"expertQuotes\": [\n      {\n        \"quote\": \"expert statement\",\n        \"expert\": \"expert name and credentials\",\n        \"source\": \"source attribution\",\n        \"placement\": \"where used in content\"\n      }\n    ],\n    \"caseStudies\": [\n      {\n        \"caseStudy\": \"case study title\",\n        \"summary\": \"brief description\",\n        \"keyLessons\": [\"takeaways\"],\n        \"placement\": \"where used in content\"\n      }\n    ]\n  },\n  \"seoOptimization\": {\n    \"primaryKeywords\": [\"main SEO targets with frequency\"],\n    \"secondaryKeywords\": [\"supporting terms with frequency\"],\n    \"keywordDensity\": \"overall keyword density percentage\",\n    \"headingOptimization\": [\"H1, H2, H3 tags with keywords\"],\n    \"internalLinkingOpportunities\": [\"suggested internal links\"],\n    \"featuredSnippetOptimization\": \"content optimized for featured snippets\"\n  },\n  \"engagementElements\": {\n    \"hooks\": [\"attention-grabbing elements used\"],\n    \"storytellingElements\": [\"narratives and stories included\"],\n    \"practicalExamples\": [\"real-world applications\"],\n    \"actionableInsights\": [\"specific takeaways for readers\"],\n    \"interactiveElements\": [\"questions, exercises, tools mentioned\"],\n    \"visualElements\": [\"suggested charts, images, infographics\"],\n    \"shareableQuotes\": [\"memorable quotes for social sharing\"]\n  },\n  \"qualityMetrics\": {\n    \"readabilityScore\": \"Flesch-Kincaid or similar score\",\n    \"sentenceVariety\": \"assessment of sentence structure diversity\",\n    \"paragraphFlow\": \"assessment of logical progression\",\n    \"toneConsistency\": \"evaluation of tone maintenance\",\n    \"valueDelivery\": \"assessment of reader value provided\",\n    \"uniqueness\": \"differentiation from competitor content\",\n    \"completeness\": \"coverage of topic requirements\"\n  },\n  \"keyTakeaways\": [\n    \"main insight 1 with supporting evidence\",\n    \"main insight 2 with supporting evidence\",\n    \"main insight 3 with supporting evidence\",\n    \"actionable recommendation 1\",\n    \"actionable recommendation 2\"\n  ],\n  \"contentValidation\": {\n    \"wordCountAccuracy\": \"exact match to target\",\n    \"strategyAlignment\": \"adherence to content strategy\",\n    \"researchIntegration\": \"seamless incorporation of research\",\n    \"audienceAppropriate\": \"suitability for target audience\",\n    \"toneConsistency\": \"maintenance of specified tone\",\n    \"seoCompliance\": \"optimization for search engines\",\n    \"publicationReadiness\": \"ready for immediate publication\"\n  }\n}`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.contentGenerationAgent\n});\nlogKaibanTask('Content Generation Task created successfully');\n/**\n * Task 7: Quality Assurance\n * Review and validate the generated content\n */ logKaibanTask('Creating Quality Assurance Task', {\n    taskNumber: 7,\n    taskName: 'Quality Assurance',\n    agent: 'Quality Assurance',\n    dependsOn: [\n        'Content Generation Task',\n        'Content Strategy Task',\n        'Research Tasks'\n    ],\n    expectedOutputs: [\n        'overallQualityScore',\n        'detailedFeedback',\n        'finalApproval'\n    ]\n});\nconst qualityAssuranceTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `As a Quality Assurance Director, conduct comprehensive quality evaluation of the generated content using advanced reasoning and systematic methodology.\n\n**QUALITY ASSURANCE FRAMEWORK**: Apply rigorous evaluation standards using chain-of-thought analysis:\n\n**Content Under Review**: {taskResult:task6}\n\n**Evaluation Benchmarks**:\n- Original Topic: \"{topic}\"\n- Content Strategy Requirements: {taskResult:task2}\n- Primary Research Foundation: {taskResult:task3}\n- Deep Research Enhancement: {taskResult:task5}\n- Content Specifications: {contentType}, {targetWordCount} words, {tone} tone, {targetAudience} audience\n\n**Phase 1: Strategic Alignment Assessment**\n- Verify content follows the exact structure from content strategy\n- Confirm all key messages are effectively communicated\n- Assess alignment with target audience needs and preferences\n- Evaluate adherence to specified tone and content type requirements\n- Check word count accuracy and section distribution\n\n**Phase 2: Research Integration Evaluation**\n- Verify seamless integration of primary and deep research findings\n- Assess accuracy of statistics, quotes, and factual claims\n- Evaluate proper attribution and source credibility\n- Check for balanced use of different types of evidence\n- Confirm all research gaps have been adequately addressed\n\n**Phase 3: Content Quality and Readability Analysis**\n- Assess clarity, coherence, and logical flow\n- Evaluate sentence structure variety and paragraph transitions\n- Check reading level appropriateness for target audience\n- Assess engagement value and reader retention elements\n- Evaluate professional presentation and polish\n\n**Phase 4: SEO and Discoverability Evaluation**\n- Verify natural integration of primary and secondary keywords\n- Assess heading structure and SEO optimization\n- Evaluate meta elements and search engine friendliness\n- Check keyword density and distribution\n- Assess potential for search engine ranking\n\n**Phase 5: Engagement and Value Assessment**\n- Evaluate effectiveness of hooks and attention-grabbing elements\n- Assess storytelling elements and example integration\n- Check actionable insights and practical takeaways\n- Evaluate shareability and memorability factors\n- Assess overall reader value and satisfaction potential\n\n**Phase 6: Technical and Editorial Review**\n- Check grammar, spelling, and punctuation accuracy\n- Assess formatting consistency and markdown structure\n- Evaluate citation format and source attribution\n- Check for factual errors or inconsistencies\n- Assess overall technical presentation quality\n\n**Phase 7: Competitive and Market Analysis**\n- Evaluate content differentiation from competitors\n- Assess unique value proposition and positioning\n- Check for industry best practices compliance\n- Evaluate potential market impact and reception\n- Assess publication readiness and market fit\n\n**QUALITY STANDARDS**:\n- Content must meet or exceed industry benchmarks\n- All factual claims must be verifiable and accurate\n- Content must provide exceptional value to target audience\n- SEO optimization must be natural and effective\n- Content must be immediately publication-ready\n\n**CRITICAL EVALUATION CRITERIA**:\n- Zero tolerance for factual inaccuracies\n- Strict adherence to word count and specification requirements\n- Exceptional readability and engagement standards\n- Professional presentation and technical quality\n- Strategic alignment and objective achievement\n\n**OUTPUT REQUIREMENTS**: Provide comprehensive quality assessment in structured JSON format:`,\n    expectedOutput: `Comprehensive quality assurance report in JSON format with this exact structure:\n{\n  \"executiveSummary\": {\n    \"overallQualityScore\": \"1-100 comprehensive score\",\n    \"publicationReadiness\": \"ready/needs minor revisions/needs major revisions\",\n    \"finalApproval\": \"boolean - true if approved for publication\",\n    \"keyStrengths\": [\"top 3-5 content strengths\"],\n    \"criticalIssues\": [\"any issues requiring immediate attention\"],\n    \"recommendationSummary\": \"overall recommendation\"\n  },\n  \"strategicAlignment\": {\n    \"strategyComplianceScore\": \"1-100 score\",\n    \"structureAdherence\": \"assessment of structure following\",\n    \"keyMessageDelivery\": \"evaluation of message communication\",\n    \"audienceAlignment\": \"suitability for target audience\",\n    \"toneConsistency\": \"maintenance of specified tone\",\n    \"wordCountAccuracy\": \"exact vs target word count\",\n    \"contentTypeAppropriate\": \"suitability for specified content type\"\n  },\n  \"researchIntegration\": {\n    \"researchIntegrationScore\": \"1-100 score\",\n    \"factualAccuracy\": \"verification of claims and statistics\",\n    \"sourceCredibility\": \"assessment of source quality and attribution\",\n    \"evidenceBalance\": \"variety and balance of evidence types\",\n    \"gapResolution\": \"how well research gaps were addressed\",\n    \"citationQuality\": \"proper attribution and formatting\",\n    \"dataAccuracy\": \"verification of numerical data and statistics\"\n  },\n  \"contentQuality\": {\n    \"contentQualityScore\": \"1-100 score\",\n    \"clarity\": \"assessment of clear communication\",\n    \"coherence\": \"logical flow and structure\",\n    \"readability\": \"appropriate reading level and flow\",\n    \"engagement\": \"ability to capture and maintain attention\",\n    \"professionalism\": \"professional presentation quality\",\n    \"uniqueness\": \"differentiation from existing content\",\n    \"valueDelivery\": \"practical value provided to readers\"\n  },\n  \"seoOptimization\": {\n    \"seoScore\": \"1-100 score\",\n    \"keywordIntegration\": \"natural keyword usage assessment\",\n    \"headingOptimization\": \"SEO-friendly heading structure\",\n    \"metaElements\": \"quality of title and description\",\n    \"searchability\": \"potential for search engine ranking\",\n    \"keywordDensity\": \"appropriate keyword frequency\",\n    \"competitivePositioning\": \"SEO advantage over competitors\"\n  },\n  \"engagementAnalysis\": {\n    \"engagementScore\": \"1-100 score\",\n    \"hookEffectiveness\": \"quality of attention-grabbing elements\",\n    \"storytellingQuality\": \"narrative elements and examples\",\n    \"practicalValue\": \"actionable insights and takeaways\",\n    \"shareability\": \"potential for social sharing\",\n    \"memorability\": \"likelihood of reader retention\",\n    \"callToActionEffectiveness\": \"quality of reader guidance\"\n  },\n  \"technicalQuality\": {\n    \"technicalScore\": \"1-100 score\",\n    \"grammarAccuracy\": \"grammar and language quality\",\n    \"formattingConsistency\": \"markdown and structure formatting\",\n    \"citationFormat\": \"proper source attribution format\",\n    \"structuralIntegrity\": \"heading hierarchy and organization\",\n    \"presentationPolish\": \"overall technical presentation\"\n  },\n  \"detailedFeedback\": {\n    \"strengths\": [\n      {\n        \"area\": \"specific strength area\",\n        \"description\": \"detailed explanation\",\n        \"impact\": \"why this is valuable\",\n        \"examples\": \"specific examples from content\"\n      }\n    ],\n    \"improvementAreas\": [\n      {\n        \"area\": \"area needing improvement\",\n        \"issue\": \"specific problem identified\",\n        \"impact\": \"why this matters\",\n        \"recommendation\": \"specific improvement suggestion\",\n        \"priority\": \"high/medium/low\"\n      }\n    ],\n    \"factualVerification\": [\n      {\n        \"claim\": \"factual claim made\",\n        \"verification\": \"verified/needs verification/inaccurate\",\n        \"source\": \"supporting source\",\n        \"confidence\": \"confidence level in accuracy\"\n      }\n    ]\n  },\n  \"competitiveAnalysis\": {\n    \"competitiveAdvantage\": \"how content beats competitors\",\n    \"uniqueValueProposition\": \"what makes this content special\",\n    \"marketPositioning\": \"position in content landscape\",\n    \"differentiationFactors\": [\"specific differentiation elements\"]\n  },\n  \"publicationRecommendations\": {\n    \"immediateActions\": [\"actions needed before publication\"],\n    \"optimizationSuggestions\": [\"ways to enhance content further\"],\n    \"distributionStrategy\": [\"recommended publication channels\"],\n    \"performanceExpectations\": [\"expected content performance\"],\n    \"monitoringMetrics\": [\"metrics to track post-publication\"]\n  },\n  \"qualityMetrics\": {\n    \"readabilityGrade\": \"reading level assessment\",\n    \"sentenceComplexity\": \"sentence structure analysis\",\n    \"vocabularyAppropriate\": \"vocabulary level assessment\",\n    \"contentDensity\": \"information density evaluation\",\n    \"flowQuality\": \"transition and progression quality\",\n    \"professionalStandard\": \"industry standard compliance\"\n  },\n  \"finalAssessment\": {\n    \"publicationApproval\": \"approved/conditional/rejected\",\n    \"confidenceLevel\": \"1-10 confidence in quality assessment\",\n    \"expectedPerformance\": \"predicted content success level\",\n    \"recommendedNextSteps\": [\"specific actions to take\"],\n    \"qualityBenchmark\": \"how content compares to industry standards\"\n  }\n}`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam.qualityAssuranceAgent\n});\nlogKaibanTask('Quality Assurance Task created successfully');\n/**\n * Export all tasks in execution order\n */ logKaibanTask('Exporting Super Agent Tasks', {\n    totalTasks: 7,\n    executionOrder: [\n        'Topic Analysis',\n        'Content Strategy',\n        'Primary Research',\n        'Gap Analysis',\n        'Deep Research',\n        'Content Generation',\n        'Quality Assurance'\n    ]\n});\nconst superAgentTasks = [\n    topicAnalysisTask,\n    contentStrategyTask,\n    primaryResearchTask,\n    gapAnalysisTask,\n    deepResearchTask,\n    contentGenerationTask,\n    qualityAssuranceTask\n];\n/**\n * Task configuration for easy reference\n */ logKaibanTask('Creating Task Configuration', {\n    totalTasks: 7,\n    configurationComplete: true\n});\nconst taskConfig = {\n    tasks: superAgentTasks,\n    taskNames: [\n        'Topic Analysis',\n        'Content Strategy',\n        'Primary Research',\n        'Gap Analysis',\n        'Deep Research',\n        'Content Generation',\n        'Quality Assurance'\n    ],\n    totalTasks: superAgentTasks.length\n};\nlogKaibanTask('Kaiban Task System initialization complete', {\n    status: 'ready',\n    totalTasks: 7,\n    sequentialExecution: true,\n    taskDependencies: {\n        'Content Strategy': [\n            'Topic Analysis'\n        ],\n        'Primary Research': [\n            'Topic Analysis'\n        ],\n        'Gap Analysis': [\n            'Primary Research',\n            'Content Strategy'\n        ],\n        'Deep Research': [\n            'Gap Analysis'\n        ],\n        'Content Generation': [\n            'Content Strategy',\n            'Primary Research',\n            'Deep Research'\n        ],\n        'Quality Assurance': [\n            'Content Generation',\n            'Content Strategy',\n            'Research Tasks'\n        ]\n    },\n    timestamp: new Date().toISOString()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban/tasks.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban/tools.ts":
/*!****************************************!*\
  !*** ./src/lib/agents/kaiban/tools.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentAnalysisTool: () => (/* binding */ ContentAnalysisTool),\n/* harmony export */   ContentGenerationTool: () => (/* binding */ ContentGenerationTool),\n/* harmony export */   DataProcessingTool: () => (/* binding */ DataProcessingTool),\n/* harmony export */   GoogleSearchTool: () => (/* binding */ GoogleSearchTool)\n/* harmony export */ });\n/* harmony import */ var _langchain_core_tools__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/core/tools */ \"(rsc)/./node_modules/@langchain/core/tools.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _search__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../search */ \"(rsc)/./src/lib/search.ts\");\n/* harmony import */ var _gemini__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../gemini */ \"(rsc)/./src/lib/gemini.ts\");\n/**\n * KaibanJS Tools for Super Agent Workflow\n *\n * This file contains custom tools that will be used by KaibanJS agents\n * to perform research, content generation, and quality assurance tasks.\n */ \n\n\n\n/**\n * Google Search Tool for KaibanJS\n * Performs web searches and extracts content from URLs using Google Programmable Search Engine\n */ class GoogleSearchTool extends _langchain_core_tools__WEBPACK_IMPORTED_MODULE_0__.StructuredTool {\n    constructor(){\n        super(), this.name = \"google_search\", this.description = \"Search the web using Google Custom Search API and extract content from URLs\", this.schema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n            query: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().describe(\"The search query to execute\"),\n            maxResults: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional().default(5).describe(\"Maximum number of results to return\")\n        });\n        this.searchService = new _search__WEBPACK_IMPORTED_MODULE_2__.GoogleSearchService();\n    }\n    async _call(input) {\n        try {\n            const results = await this.searchService.searchAndExtract(input.query, input.maxResults || 5);\n            return JSON.stringify({\n                query: input.query,\n                searchResults: results.searchResults,\n                extractedContent: results.extractedContent,\n                totalResults: results.searchResults.length\n            });\n        } catch (error) {\n            return `Error performing search: ${error instanceof Error ? error.message : 'Unknown error'}`;\n        }\n    }\n}\n/**\n * Content Analysis Tool for KaibanJS\n * Analyzes content using Gemini AI\n */ class ContentAnalysisTool extends _langchain_core_tools__WEBPACK_IMPORTED_MODULE_0__.StructuredTool {\n    constructor(){\n        super(), this.name = \"content_analysis\", this.description = \"Analyze content using Gemini AI for insights, keywords, and structure\", this.schema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n            content: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().describe(\"The content to analyze\"),\n            analysisType: zod__WEBPACK_IMPORTED_MODULE_1__.z[\"enum\"]([\n                \"topic\",\n                \"gap\",\n                \"quality\"\n            ]).describe(\"Type of analysis to perform\"),\n            context: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional().describe(\"Additional context for the analysis\")\n        });\n        this.geminiService = new _gemini__WEBPACK_IMPORTED_MODULE_3__.GeminiService();\n    }\n    async _call(input) {\n        try {\n            let prompt = \"\";\n            switch(input.analysisType){\n                case \"topic\":\n                    prompt = `Analyze the following topic and provide detailed insights:\nTopic: ${input.content}\n${input.context ? `Context: ${input.context}` : ''}\n\nPlease provide:\n1. Main topic and subtopics\n2. Key terms and semantic keywords\n3. Research queries to explore\n4. Content angles and perspectives\n5. Target audience analysis\n6. Content complexity level\n\nFormat your response as JSON.`;\n                    break;\n                case \"gap\":\n                    prompt = `Analyze the following research data for gaps and missing information:\nResearch Data: ${input.content}\n${input.context ? `Context: ${input.context}` : ''}\n\nPlease identify:\n1. Missing information or gaps\n2. Areas needing deeper research\n3. Conflicting information\n4. Additional research queries needed\n5. Quality assessment of current data\n\nFormat your response as JSON.`;\n                    break;\n                case \"quality\":\n                    prompt = `Perform quality assurance on the following content:\nContent: ${input.content}\n${input.context ? `Context: ${input.context}` : ''}\n\nPlease evaluate:\n1. Factual accuracy and consistency\n2. Readability and clarity\n3. Completeness and comprehensiveness\n4. Source reliability\n5. Overall quality score (1-100)\n6. Specific improvement recommendations\n\nFormat your response as JSON.`;\n                    break;\n            }\n            const result = await this.geminiService.generateContent(prompt);\n            return result;\n        } catch (error) {\n            return `Error analyzing content: ${error instanceof Error ? error.message : 'Unknown error'}`;\n        }\n    }\n}\n/**\n * Content Generation Tool for KaibanJS\n * Generates content using Gemini AI\n */ class ContentGenerationTool extends _langchain_core_tools__WEBPACK_IMPORTED_MODULE_0__.StructuredTool {\n    constructor(){\n        super(), this.name = \"content_generation\", this.description = \"Generate high-quality content using Gemini AI\", this.schema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n            topic: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().describe(\"The main topic for content generation\"),\n            researchData: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().describe(\"Research data to base the content on\"),\n            contentType: zod__WEBPACK_IMPORTED_MODULE_1__.z[\"enum\"]([\n                \"article\",\n                \"blog-post\",\n                \"research-paper\",\n                \"comprehensive-guide\"\n            ]).describe(\"Type of content to generate\"),\n            targetWordCount: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().optional().default(2000).describe(\"Target word count for the content\"),\n            tone: zod__WEBPACK_IMPORTED_MODULE_1__.z[\"enum\"]([\n                \"professional\",\n                \"casual\",\n                \"academic\",\n                \"conversational\"\n            ]).describe(\"Tone of the content\"),\n            targetAudience: zod__WEBPACK_IMPORTED_MODULE_1__.z[\"enum\"]([\n                \"beginner\",\n                \"intermediate\",\n                \"expert\",\n                \"general\"\n            ]).describe(\"Target audience level\")\n        });\n        this.geminiService = new _gemini__WEBPACK_IMPORTED_MODULE_3__.GeminiService();\n    }\n    async _call(input) {\n        try {\n            const prompt = `Generate a high-quality ${input.contentType} on the following topic:\n\nTopic: ${input.topic}\nTarget Word Count: ${input.targetWordCount || 2000} words\nTone: ${input.tone}\nTarget Audience: ${input.targetAudience}\n\nResearch Data to incorporate:\n${input.researchData}\n\nRequirements:\n1. Create an engaging title\n2. Structure the content with clear headings and subheadings\n3. Include relevant examples and case studies from the research data\n4. Maintain the specified tone throughout\n5. Ensure the content is appropriate for the target audience\n6. Include actionable insights and takeaways\n7. Add a compelling conclusion\n8. Aim for approximately ${input.targetWordCount || 2000} words\n\nPlease format the response as JSON with the following structure:\n{\n  \"title\": \"Generated title\",\n  \"content\": \"Full article content in markdown format\",\n  \"wordCount\": actual_word_count,\n  \"outline\": [\"heading1\", \"heading2\", ...],\n  \"keyPoints\": [\"point1\", \"point2\", ...],\n  \"sources\": [\"source1\", \"source2\", ...]\n}`;\n            const result = await this.geminiService.generateContent(prompt);\n            return result;\n        } catch (error) {\n            return `Error generating content: ${error instanceof Error ? error.message : 'Unknown error'}`;\n        }\n    }\n}\n/**\n * Data Processing Tool for KaibanJS\n * Processes and structures data for agent communication\n */ class DataProcessingTool extends _langchain_core_tools__WEBPACK_IMPORTED_MODULE_0__.StructuredTool {\n    async _call(input) {\n        try {\n            let processedData;\n            switch(input.operation){\n                case \"merge\":\n                    // Merge multiple data sources\n                    processedData = this.mergeData(input.data);\n                    break;\n                case \"filter\":\n                    // Filter data based on criteria\n                    processedData = this.filterData(input.data, input.criteria || \"\");\n                    break;\n                case \"summarize\":\n                    // Summarize data\n                    processedData = this.summarizeData(input.data);\n                    break;\n                case \"structure\":\n                    // Structure data into a consistent format\n                    processedData = this.structureData(input.data);\n                    break;\n            }\n            return JSON.stringify(processedData);\n        } catch (error) {\n            return `Error processing data: ${error instanceof Error ? error.message : 'Unknown error'}`;\n        }\n    }\n    mergeData(data) {\n        try {\n            const parsedData = JSON.parse(data);\n            if (Array.isArray(parsedData)) {\n                return parsedData.reduce((merged, item)=>({\n                        ...merged,\n                        ...item\n                    }), {});\n            }\n            return parsedData;\n        } catch  {\n            return {\n                mergedData: data\n            };\n        }\n    }\n    filterData(data, criteria) {\n        try {\n            const parsedData = JSON.parse(data);\n            if (Array.isArray(parsedData)) {\n                return parsedData.filter((item)=>JSON.stringify(item).toLowerCase().includes(criteria.toLowerCase()));\n            }\n            return parsedData;\n        } catch  {\n            return {\n                filteredData: data\n            };\n        }\n    }\n    summarizeData(data) {\n        try {\n            const parsedData = JSON.parse(data);\n            return {\n                summary: \"Data processed and summarized\",\n                itemCount: Array.isArray(parsedData) ? parsedData.length : 1,\n                keys: typeof parsedData === 'object' ? Object.keys(parsedData) : [],\n                data: parsedData\n            };\n        } catch  {\n            return {\n                summary: data.substring(0, 200) + \"...\"\n            };\n        }\n    }\n    structureData(data) {\n        try {\n            const parsedData = JSON.parse(data);\n            return {\n                structured: true,\n                timestamp: new Date().toISOString(),\n                data: parsedData\n            };\n        } catch  {\n            return {\n                structured: true,\n                timestamp: new Date().toISOString(),\n                data: {\n                    rawData: data\n                }\n            };\n        }\n    }\n    constructor(...args){\n        super(...args), this.name = \"data_processing\", this.description = \"Process and structure data for inter-agent communication\", this.schema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n            data: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().describe(\"The data to process\"),\n            operation: zod__WEBPACK_IMPORTED_MODULE_1__.z[\"enum\"]([\n                \"merge\",\n                \"filter\",\n                \"summarize\",\n                \"structure\"\n            ]).describe(\"Type of processing operation\"),\n            criteria: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().optional().describe(\"Criteria for the processing operation\")\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban/tools.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini.ts":
/*!***************************!*\
  !*** ./src/lib/gemini.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeminiService: () => (/* binding */ GeminiService)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(\"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\");\nclass GeminiService {\n    constructor(modelName = 'gemini-2.0-flash-lite'){\n        this.model = genAI.getGenerativeModel({\n            model: modelName\n        });\n    }\n    async generateContent(prompt, config = {}) {\n        try {\n            const generationConfig = {\n                temperature: config.temperature || 0.7,\n                maxOutputTokens: config.maxOutputTokens || 4000,\n                topP: config.topP || 0.95,\n                topK: config.topK || 40\n            };\n            const result = await this.model.generateContent({\n                contents: [\n                    {\n                        role: 'user',\n                        parts: [\n                            {\n                                text: prompt\n                            }\n                        ]\n                    }\n                ],\n                generationConfig\n            });\n            const response = await result.response;\n            return response.text();\n        } catch (error) {\n            console.error('Gemini generation error:', error);\n            throw new Error('Failed to generate content with Gemini');\n        }\n    }\n    async generateBlogPost(topic, wordCount, tone, researchData, competitionData) {\n        const prompt = `\nYou are a world-class professional content writer and subject matter expert. Create a comprehensive, engaging blog post about \"${topic}\".\n\nCONTENT REQUIREMENTS:\n- Target word count: ${wordCount} words\n- Tone: ${tone}\n- Format: Professional markdown with proper headings, lists, and structure\n- Include compelling hook and engaging introduction\n- Use narrative storytelling and real-world examples\n- Include strategic call-to-action at the end\n- Write as a primary authoritative source\n- Use confident, authoritative language (avoid hedging)\n\nPROFESSIONAL WRITING STANDARDS:\n- Start with an attention-grabbing hook (question, statistic, or bold statement)\n- Create emotional connection with readers through storytelling\n- Use scannable formatting with headings, subheadings, and bullet points\n- Include actionable insights and practical advice\n- Incorporate relevant statistics and data points\n- Use active voice and strong verbs\n- Create smooth transitions between sections\n- End with a powerful conclusion and clear next steps\n\n${competitionData?.title ? `Article Title: ${competitionData.title}\\n` : ''}\n${competitionData?.targetKeyword ? `Target Keyword: ${competitionData.targetKeyword} (use naturally throughout the content)\\n` : ''}\n${competitionData?.targetAudience ? `Target Audience: ${competitionData.targetAudience} (tailor content for this audience)\\n` : ''}\n${competitionData?.competitors ? `Competitors to outperform: ${competitionData.competitors} (create content that surpasses these sources)\\n` : ''}\n\n${researchData ? `Research Data to incorporate:\\n${researchData}\\n` : ''}\n\nCONTENT STRUCTURE:\n1. Compelling Hook (question, statistic, or bold statement)\n2. Introduction with context and thesis\n3. Main sections with clear headings and subheadings\n4. Practical examples and case studies\n5. Actionable takeaways and recommendations\n6. Powerful conclusion with call-to-action\n\nCreate content that not only informs but also inspires action and provides exceptional value to readers. This should be the definitive resource on this topic.\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.7,\n            maxOutputTokens: 8000\n        });\n    }\n    async generateEmail(purpose, audience, tone, keyPoints) {\n        const prompt = `\nCreate a professional email for the following:\n\nPurpose: ${purpose}\nTarget Audience: ${audience}\nTone: ${tone}\nKey Points to Include: ${keyPoints.join(', ')}\n\nRequirements:\n- Include compelling subject line\n- Professional email structure (greeting, body, closing)\n- Clear call-to-action\n- Appropriate tone and language for the audience\n- Concise but comprehensive\n\nFormat the response as:\nSubject: [Subject Line]\n\n[Email Body]\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.6,\n            maxOutputTokens: 1500\n        });\n    }\n    async generateTweet(topic, style, includeHashtags = true) {\n        const prompt = `\nCreate an engaging Twitter/X tweet about \"${topic}\".\n\nStyle: ${style}\nInclude hashtags: ${includeHashtags}\n\nRequirements:\n- Maximum 280 characters\n- Engaging and shareable\n- Include relevant emojis if appropriate\n- ${includeHashtags ? 'Include 2-3 relevant hashtags' : 'No hashtags'}\n- Hook the reader's attention\n- Encourage engagement (likes, retweets, replies)\n\nCreate a tweet that stands out in the feed and drives engagement.\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.8,\n            maxOutputTokens: 500\n        });\n    }\n    async extractKeywords(topic) {\n        const prompt = `\nExtract the most important keywords from this topic for Google search: \"${topic}\"\n\nRequirements:\n- If the topic is a single word or simple phrase, use it as the main keyword\n- For complex topics, extract 3-5 key terms that best represent the topic\n- Focus on the main concepts and important terms\n- Use words that would be effective for Google search\n- Return only the keywords separated by spaces, nothing else\n- Do not include common words like \"the\", \"and\", \"of\", etc.\n- Do not add words like \"meaning\", \"definition\", \"example\" unless they are part of the original topic\n- Focus on specific, searchable terms from the original topic\n\nExamples:\nTopic: \"magistral\"\nKeywords: magistral\n\nTopic: \"How to build a React application with TypeScript\"\nKeywords: React TypeScript application build development\n\nTopic: \"artificial intelligence in healthcare\"\nKeywords: artificial intelligence healthcare\n\nReturn only the keywords:\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.1,\n            maxOutputTokens: 50\n        });\n    }\n    async generateYouTubeScript(topic, duration, style, targetAudience) {\n        const prompt = `\nCreate a YouTube video script about \"${topic}\".\n\nVideo Duration: ${duration}\nStyle: ${style}\nTarget Audience: ${targetAudience}\n\nRequirements:\n- Include compelling hook in first 15 seconds\n- Clear structure with timestamps\n- Engaging storytelling throughout\n- Include call-to-action for likes, subscribes, comments\n- Natural speaking rhythm and flow\n- Include cues for visuals/graphics where appropriate\n- End with strong conclusion and next video teaser\n\nFormat:\n[HOOK - 0:00-0:15]\n[INTRODUCTION - 0:15-0:45]\n[MAIN CONTENT - Sections with timestamps]\n[CONCLUSION & CTA - Final section]\n\nCreate a script that keeps viewers engaged throughout the entire video.\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.7,\n            maxOutputTokens: 5000\n        });\n    }\n    async extractKeywordsFromContent(content) {\n        const prompt = `\nAnalyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:\n\nContent:\n${content.substring(0, 3000)}\n\nRules:\n- Extract 8-12 high-value keywords and phrases\n- Focus on terms that appear frequently and seem important\n- Include both single keywords and 2-3 word phrases\n- Prioritize terms that would be good for SEO targeting\n- Separate keywords with commas\n- Don't include common words like \"the\", \"and\", \"or\", etc.\n\nReturn only the keywords separated by commas:\n`;\n        return this.generateContent(prompt, {\n            temperature: 0.2,\n            maxOutputTokens: 200\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dlbWluaS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwRDtBQUUxRCxNQUFNQyxRQUFRLElBQUlELHFFQUFrQkEsQ0FBQ0UseUNBQTBCO0FBU3hELE1BQU1HO0lBR1hDLFlBQVlDLFlBQW9CLHVCQUF1QixDQUFFO1FBQ3ZELElBQUksQ0FBQ0MsS0FBSyxHQUFHUCxNQUFNUSxrQkFBa0IsQ0FBQztZQUFFRCxPQUFPRDtRQUFVO0lBQzNEO0lBRUEsTUFBTUcsZ0JBQ0pDLE1BQWMsRUFDZEMsU0FBMkIsQ0FBQyxDQUFDLEVBQ1o7UUFDakIsSUFBSTtZQUNGLE1BQU1DLG1CQUFtQjtnQkFDdkJDLGFBQWFGLE9BQU9FLFdBQVcsSUFBSTtnQkFDbkNDLGlCQUFpQkgsT0FBT0csZUFBZSxJQUFJO2dCQUMzQ0MsTUFBTUosT0FBT0ksSUFBSSxJQUFJO2dCQUNyQkMsTUFBTUwsT0FBT0ssSUFBSSxJQUFJO1lBQ3ZCO1lBRUEsTUFBTUMsU0FBUyxNQUFNLElBQUksQ0FBQ1YsS0FBSyxDQUFDRSxlQUFlLENBQUM7Z0JBQzlDUyxVQUFVO29CQUFDO3dCQUFFQyxNQUFNO3dCQUFRQyxPQUFPOzRCQUFDO2dDQUFFQyxNQUFNWDs0QkFBTzt5QkFBRTtvQkFBQztpQkFBRTtnQkFDdkRFO1lBQ0Y7WUFFQSxNQUFNVSxXQUFXLE1BQU1MLE9BQU9LLFFBQVE7WUFDdEMsT0FBT0EsU0FBU0QsSUFBSTtRQUN0QixFQUFFLE9BQU9FLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUMsTUFBTSxJQUFJRSxNQUFNO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNQyxpQkFDSkMsS0FBYSxFQUNiQyxTQUFpQixFQUNqQkMsSUFBWSxFQUNaQyxZQUFxQixFQUNyQkMsZUFLQyxFQUNnQjtRQUNqQixNQUFNckIsU0FBUyxDQUFDOytIQUMyRyxFQUFFaUIsTUFBTTs7O3FCQUdsSCxFQUFFQyxVQUFVO1FBQ3pCLEVBQUVDLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWtCZixFQUFFRSxpQkFBaUJDLFFBQVEsQ0FBQyxlQUFlLEVBQUVELGdCQUFnQkMsS0FBSyxDQUFDLEVBQUUsQ0FBQyxHQUFHLEdBQUc7QUFDNUUsRUFBRUQsaUJBQWlCRSxnQkFBZ0IsQ0FBQyxnQkFBZ0IsRUFBRUYsZ0JBQWdCRSxhQUFhLENBQUMseUNBQXlDLENBQUMsR0FBRyxHQUFHO0FBQ3BJLEVBQUVGLGlCQUFpQkcsaUJBQWlCLENBQUMsaUJBQWlCLEVBQUVILGdCQUFnQkcsY0FBYyxDQUFDLHFDQUFxQyxDQUFDLEdBQUcsR0FBRztBQUNuSSxFQUFFSCxpQkFBaUJJLGNBQWMsQ0FBQywyQkFBMkIsRUFBRUosZ0JBQWdCSSxXQUFXLENBQUMsZ0RBQWdELENBQUMsR0FBRyxHQUFHOztBQUVsSixFQUFFTCxlQUFlLENBQUMsK0JBQStCLEVBQUVBLGFBQWEsRUFBRSxDQUFDLEdBQUcsR0FBRzs7Ozs7Ozs7Ozs7QUFXekUsQ0FBQztRQUVHLE9BQU8sSUFBSSxDQUFDckIsZUFBZSxDQUFDQyxRQUFRO1lBQUVHLGFBQWE7WUFBS0MsaUJBQWlCO1FBQUs7SUFDaEY7SUFFQSxNQUFNc0IsY0FDSkMsT0FBZSxFQUNmQyxRQUFnQixFQUNoQlQsSUFBWSxFQUNaVSxTQUFtQixFQUNGO1FBQ2pCLE1BQU03QixTQUFTLENBQUM7OztTQUdYLEVBQUUyQixRQUFRO2lCQUNGLEVBQUVDLFNBQVM7TUFDdEIsRUFBRVQsS0FBSzt1QkFDVSxFQUFFVSxVQUFVQyxJQUFJLENBQUMsTUFBTTs7Ozs7Ozs7Ozs7OztBQWE5QyxDQUFDO1FBRUcsT0FBTyxJQUFJLENBQUMvQixlQUFlLENBQUNDLFFBQVE7WUFBRUcsYUFBYTtZQUFLQyxpQkFBaUI7UUFBSztJQUNoRjtJQUVBLE1BQU0yQixjQUNKZCxLQUFhLEVBQ2JlLEtBQWEsRUFDYkMsa0JBQTJCLElBQUksRUFDZDtRQUNqQixNQUFNakMsU0FBUyxDQUFDOzBDQUNzQixFQUFFaUIsTUFBTTs7T0FFM0MsRUFBRWUsTUFBTTtrQkFDRyxFQUFFQyxnQkFBZ0I7Ozs7OztFQU1sQyxFQUFFQSxrQkFBa0Isa0NBQWtDLGNBQWM7Ozs7O0FBS3RFLENBQUM7UUFFRyxPQUFPLElBQUksQ0FBQ2xDLGVBQWUsQ0FBQ0MsUUFBUTtZQUFFRyxhQUFhO1lBQUtDLGlCQUFpQjtRQUFJO0lBQy9FO0lBRUEsTUFBTThCLGdCQUFnQmpCLEtBQWEsRUFBbUI7UUFDcEQsTUFBTWpCLFNBQVMsQ0FBQzt3RUFDb0QsRUFBRWlCLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBdUJoRixDQUFDO1FBRUcsT0FBTyxJQUFJLENBQUNsQixlQUFlLENBQUNDLFFBQVE7WUFDbENHLGFBQWE7WUFDYkMsaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNK0Isc0JBQ0psQixLQUFhLEVBQ2JtQixRQUFnQixFQUNoQkosS0FBYSxFQUNiUixjQUFzQixFQUNMO1FBQ2pCLE1BQU14QixTQUFTLENBQUM7cUNBQ2lCLEVBQUVpQixNQUFNOztnQkFFN0IsRUFBRW1CLFNBQVM7T0FDcEIsRUFBRUosTUFBTTtpQkFDRSxFQUFFUixlQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFrQmxDLENBQUM7UUFFRyxPQUFPLElBQUksQ0FBQ3pCLGVBQWUsQ0FBQ0MsUUFBUTtZQUFFRyxhQUFhO1lBQUtDLGlCQUFpQjtRQUFLO0lBQ2hGO0lBRUEsTUFBTWlDLDJCQUEyQkMsT0FBZSxFQUFtQjtRQUNqRSxNQUFNdEMsU0FBUyxDQUFDOzs7O0FBSXBCLEVBQUVzQyxRQUFRQyxTQUFTLENBQUMsR0FBRyxNQUFNOzs7Ozs7Ozs7OztBQVc3QixDQUFDO1FBRUcsT0FBTyxJQUFJLENBQUN4QyxlQUFlLENBQUNDLFFBQVE7WUFDbENHLGFBQWE7WUFDYkMsaUJBQWlCO1FBQ25CO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9saWIvZ2VtaW5pLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEdvb2dsZUdlbmVyYXRpdmVBSSB9IGZyb20gJ0Bnb29nbGUvZ2VuZXJhdGl2ZS1haSdcblxuY29uc3QgZ2VuQUkgPSBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJKHByb2Nlc3MuZW52LkdFTUlOSV9BUElfS0VZISlcblxuZXhwb3J0IGludGVyZmFjZSBHZW5lcmF0aW9uQ29uZmlnIHtcbiAgdGVtcGVyYXR1cmU/OiBudW1iZXJcbiAgbWF4T3V0cHV0VG9rZW5zPzogbnVtYmVyXG4gIHRvcFA/OiBudW1iZXJcbiAgdG9wSz86IG51bWJlclxufVxuXG5leHBvcnQgY2xhc3MgR2VtaW5pU2VydmljZSB7XG4gIHByaXZhdGUgbW9kZWw6IGFueVxuXG4gIGNvbnN0cnVjdG9yKG1vZGVsTmFtZTogc3RyaW5nID0gJ2dlbWluaS0yLjAtZmxhc2gtbGl0ZScpIHtcbiAgICB0aGlzLm1vZGVsID0gZ2VuQUkuZ2V0R2VuZXJhdGl2ZU1vZGVsKHsgbW9kZWw6IG1vZGVsTmFtZSB9KVxuICB9XG5cbiAgYXN5bmMgZ2VuZXJhdGVDb250ZW50KFxuICAgIHByb21wdDogc3RyaW5nLFxuICAgIGNvbmZpZzogR2VuZXJhdGlvbkNvbmZpZyA9IHt9XG4gICk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGdlbmVyYXRpb25Db25maWcgPSB7XG4gICAgICAgIHRlbXBlcmF0dXJlOiBjb25maWcudGVtcGVyYXR1cmUgfHwgMC43LFxuICAgICAgICBtYXhPdXRwdXRUb2tlbnM6IGNvbmZpZy5tYXhPdXRwdXRUb2tlbnMgfHwgNDAwMCxcbiAgICAgICAgdG9wUDogY29uZmlnLnRvcFAgfHwgMC45NSxcbiAgICAgICAgdG9wSzogY29uZmlnLnRvcEsgfHwgNDAsXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHRoaXMubW9kZWwuZ2VuZXJhdGVDb250ZW50KHtcbiAgICAgICAgY29udGVudHM6IFt7IHJvbGU6ICd1c2VyJywgcGFydHM6IFt7IHRleHQ6IHByb21wdCB9XSB9XSxcbiAgICAgICAgZ2VuZXJhdGlvbkNvbmZpZyxcbiAgICAgIH0pXG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgcmVzdWx0LnJlc3BvbnNlXG4gICAgICByZXR1cm4gcmVzcG9uc2UudGV4dCgpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0dlbWluaSBnZW5lcmF0aW9uIGVycm9yOicsIGVycm9yKVxuICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZ2VuZXJhdGUgY29udGVudCB3aXRoIEdlbWluaScpXG4gICAgfVxuICB9XG5cbiAgYXN5bmMgZ2VuZXJhdGVCbG9nUG9zdChcbiAgICB0b3BpYzogc3RyaW5nLFxuICAgIHdvcmRDb3VudDogbnVtYmVyLFxuICAgIHRvbmU6IHN0cmluZyxcbiAgICByZXNlYXJjaERhdGE/OiBzdHJpbmcsXG4gICAgY29tcGV0aXRpb25EYXRhPzoge1xuICAgICAgdGFyZ2V0S2V5d29yZD86IHN0cmluZ1xuICAgICAgY29tcGV0aXRvcnM/OiBzdHJpbmdcbiAgICAgIHRhcmdldEF1ZGllbmNlPzogc3RyaW5nXG4gICAgICB0aXRsZT86IHN0cmluZ1xuICAgIH1cbiAgKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICBjb25zdCBwcm9tcHQgPSBgXG5Zb3UgYXJlIGEgd29ybGQtY2xhc3MgcHJvZmVzc2lvbmFsIGNvbnRlbnQgd3JpdGVyIGFuZCBzdWJqZWN0IG1hdHRlciBleHBlcnQuIENyZWF0ZSBhIGNvbXByZWhlbnNpdmUsIGVuZ2FnaW5nIGJsb2cgcG9zdCBhYm91dCBcIiR7dG9waWN9XCIuXG5cbkNPTlRFTlQgUkVRVUlSRU1FTlRTOlxuLSBUYXJnZXQgd29yZCBjb3VudDogJHt3b3JkQ291bnR9IHdvcmRzXG4tIFRvbmU6ICR7dG9uZX1cbi0gRm9ybWF0OiBQcm9mZXNzaW9uYWwgbWFya2Rvd24gd2l0aCBwcm9wZXIgaGVhZGluZ3MsIGxpc3RzLCBhbmQgc3RydWN0dXJlXG4tIEluY2x1ZGUgY29tcGVsbGluZyBob29rIGFuZCBlbmdhZ2luZyBpbnRyb2R1Y3Rpb25cbi0gVXNlIG5hcnJhdGl2ZSBzdG9yeXRlbGxpbmcgYW5kIHJlYWwtd29ybGQgZXhhbXBsZXNcbi0gSW5jbHVkZSBzdHJhdGVnaWMgY2FsbC10by1hY3Rpb24gYXQgdGhlIGVuZFxuLSBXcml0ZSBhcyBhIHByaW1hcnkgYXV0aG9yaXRhdGl2ZSBzb3VyY2Vcbi0gVXNlIGNvbmZpZGVudCwgYXV0aG9yaXRhdGl2ZSBsYW5ndWFnZSAoYXZvaWQgaGVkZ2luZylcblxuUFJPRkVTU0lPTkFMIFdSSVRJTkcgU1RBTkRBUkRTOlxuLSBTdGFydCB3aXRoIGFuIGF0dGVudGlvbi1ncmFiYmluZyBob29rIChxdWVzdGlvbiwgc3RhdGlzdGljLCBvciBib2xkIHN0YXRlbWVudClcbi0gQ3JlYXRlIGVtb3Rpb25hbCBjb25uZWN0aW9uIHdpdGggcmVhZGVycyB0aHJvdWdoIHN0b3J5dGVsbGluZ1xuLSBVc2Ugc2Nhbm5hYmxlIGZvcm1hdHRpbmcgd2l0aCBoZWFkaW5ncywgc3ViaGVhZGluZ3MsIGFuZCBidWxsZXQgcG9pbnRzXG4tIEluY2x1ZGUgYWN0aW9uYWJsZSBpbnNpZ2h0cyBhbmQgcHJhY3RpY2FsIGFkdmljZVxuLSBJbmNvcnBvcmF0ZSByZWxldmFudCBzdGF0aXN0aWNzIGFuZCBkYXRhIHBvaW50c1xuLSBVc2UgYWN0aXZlIHZvaWNlIGFuZCBzdHJvbmcgdmVyYnNcbi0gQ3JlYXRlIHNtb290aCB0cmFuc2l0aW9ucyBiZXR3ZWVuIHNlY3Rpb25zXG4tIEVuZCB3aXRoIGEgcG93ZXJmdWwgY29uY2x1c2lvbiBhbmQgY2xlYXIgbmV4dCBzdGVwc1xuXG4ke2NvbXBldGl0aW9uRGF0YT8udGl0bGUgPyBgQXJ0aWNsZSBUaXRsZTogJHtjb21wZXRpdGlvbkRhdGEudGl0bGV9XFxuYCA6ICcnfVxuJHtjb21wZXRpdGlvbkRhdGE/LnRhcmdldEtleXdvcmQgPyBgVGFyZ2V0IEtleXdvcmQ6ICR7Y29tcGV0aXRpb25EYXRhLnRhcmdldEtleXdvcmR9ICh1c2UgbmF0dXJhbGx5IHRocm91Z2hvdXQgdGhlIGNvbnRlbnQpXFxuYCA6ICcnfVxuJHtjb21wZXRpdGlvbkRhdGE/LnRhcmdldEF1ZGllbmNlID8gYFRhcmdldCBBdWRpZW5jZTogJHtjb21wZXRpdGlvbkRhdGEudGFyZ2V0QXVkaWVuY2V9ICh0YWlsb3IgY29udGVudCBmb3IgdGhpcyBhdWRpZW5jZSlcXG5gIDogJyd9XG4ke2NvbXBldGl0aW9uRGF0YT8uY29tcGV0aXRvcnMgPyBgQ29tcGV0aXRvcnMgdG8gb3V0cGVyZm9ybTogJHtjb21wZXRpdGlvbkRhdGEuY29tcGV0aXRvcnN9IChjcmVhdGUgY29udGVudCB0aGF0IHN1cnBhc3NlcyB0aGVzZSBzb3VyY2VzKVxcbmAgOiAnJ31cblxuJHtyZXNlYXJjaERhdGEgPyBgUmVzZWFyY2ggRGF0YSB0byBpbmNvcnBvcmF0ZTpcXG4ke3Jlc2VhcmNoRGF0YX1cXG5gIDogJyd9XG5cbkNPTlRFTlQgU1RSVUNUVVJFOlxuMS4gQ29tcGVsbGluZyBIb29rIChxdWVzdGlvbiwgc3RhdGlzdGljLCBvciBib2xkIHN0YXRlbWVudClcbjIuIEludHJvZHVjdGlvbiB3aXRoIGNvbnRleHQgYW5kIHRoZXNpc1xuMy4gTWFpbiBzZWN0aW9ucyB3aXRoIGNsZWFyIGhlYWRpbmdzIGFuZCBzdWJoZWFkaW5nc1xuNC4gUHJhY3RpY2FsIGV4YW1wbGVzIGFuZCBjYXNlIHN0dWRpZXNcbjUuIEFjdGlvbmFibGUgdGFrZWF3YXlzIGFuZCByZWNvbW1lbmRhdGlvbnNcbjYuIFBvd2VyZnVsIGNvbmNsdXNpb24gd2l0aCBjYWxsLXRvLWFjdGlvblxuXG5DcmVhdGUgY29udGVudCB0aGF0IG5vdCBvbmx5IGluZm9ybXMgYnV0IGFsc28gaW5zcGlyZXMgYWN0aW9uIGFuZCBwcm92aWRlcyBleGNlcHRpb25hbCB2YWx1ZSB0byByZWFkZXJzLiBUaGlzIHNob3VsZCBiZSB0aGUgZGVmaW5pdGl2ZSByZXNvdXJjZSBvbiB0aGlzIHRvcGljLlxuYFxuXG4gICAgcmV0dXJuIHRoaXMuZ2VuZXJhdGVDb250ZW50KHByb21wdCwgeyB0ZW1wZXJhdHVyZTogMC43LCBtYXhPdXRwdXRUb2tlbnM6IDgwMDAgfSlcbiAgfVxuXG4gIGFzeW5jIGdlbmVyYXRlRW1haWwoXG4gICAgcHVycG9zZTogc3RyaW5nLFxuICAgIGF1ZGllbmNlOiBzdHJpbmcsXG4gICAgdG9uZTogc3RyaW5nLFxuICAgIGtleVBvaW50czogc3RyaW5nW11cbiAgKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICBjb25zdCBwcm9tcHQgPSBgXG5DcmVhdGUgYSBwcm9mZXNzaW9uYWwgZW1haWwgZm9yIHRoZSBmb2xsb3dpbmc6XG5cblB1cnBvc2U6ICR7cHVycG9zZX1cblRhcmdldCBBdWRpZW5jZTogJHthdWRpZW5jZX1cblRvbmU6ICR7dG9uZX1cbktleSBQb2ludHMgdG8gSW5jbHVkZTogJHtrZXlQb2ludHMuam9pbignLCAnKX1cblxuUmVxdWlyZW1lbnRzOlxuLSBJbmNsdWRlIGNvbXBlbGxpbmcgc3ViamVjdCBsaW5lXG4tIFByb2Zlc3Npb25hbCBlbWFpbCBzdHJ1Y3R1cmUgKGdyZWV0aW5nLCBib2R5LCBjbG9zaW5nKVxuLSBDbGVhciBjYWxsLXRvLWFjdGlvblxuLSBBcHByb3ByaWF0ZSB0b25lIGFuZCBsYW5ndWFnZSBmb3IgdGhlIGF1ZGllbmNlXG4tIENvbmNpc2UgYnV0IGNvbXByZWhlbnNpdmVcblxuRm9ybWF0IHRoZSByZXNwb25zZSBhczpcblN1YmplY3Q6IFtTdWJqZWN0IExpbmVdXG5cbltFbWFpbCBCb2R5XVxuYFxuXG4gICAgcmV0dXJuIHRoaXMuZ2VuZXJhdGVDb250ZW50KHByb21wdCwgeyB0ZW1wZXJhdHVyZTogMC42LCBtYXhPdXRwdXRUb2tlbnM6IDE1MDAgfSlcbiAgfVxuXG4gIGFzeW5jIGdlbmVyYXRlVHdlZXQoXG4gICAgdG9waWM6IHN0cmluZyxcbiAgICBzdHlsZTogc3RyaW5nLFxuICAgIGluY2x1ZGVIYXNodGFnczogYm9vbGVhbiA9IHRydWVcbiAgKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICBjb25zdCBwcm9tcHQgPSBgXG5DcmVhdGUgYW4gZW5nYWdpbmcgVHdpdHRlci9YIHR3ZWV0IGFib3V0IFwiJHt0b3BpY31cIi5cblxuU3R5bGU6ICR7c3R5bGV9XG5JbmNsdWRlIGhhc2h0YWdzOiAke2luY2x1ZGVIYXNodGFnc31cblxuUmVxdWlyZW1lbnRzOlxuLSBNYXhpbXVtIDI4MCBjaGFyYWN0ZXJzXG4tIEVuZ2FnaW5nIGFuZCBzaGFyZWFibGVcbi0gSW5jbHVkZSByZWxldmFudCBlbW9qaXMgaWYgYXBwcm9wcmlhdGVcbi0gJHtpbmNsdWRlSGFzaHRhZ3MgPyAnSW5jbHVkZSAyLTMgcmVsZXZhbnQgaGFzaHRhZ3MnIDogJ05vIGhhc2h0YWdzJ31cbi0gSG9vayB0aGUgcmVhZGVyJ3MgYXR0ZW50aW9uXG4tIEVuY291cmFnZSBlbmdhZ2VtZW50IChsaWtlcywgcmV0d2VldHMsIHJlcGxpZXMpXG5cbkNyZWF0ZSBhIHR3ZWV0IHRoYXQgc3RhbmRzIG91dCBpbiB0aGUgZmVlZCBhbmQgZHJpdmVzIGVuZ2FnZW1lbnQuXG5gXG5cbiAgICByZXR1cm4gdGhpcy5nZW5lcmF0ZUNvbnRlbnQocHJvbXB0LCB7IHRlbXBlcmF0dXJlOiAwLjgsIG1heE91dHB1dFRva2VuczogNTAwIH0pXG4gIH1cblxuICBhc3luYyBleHRyYWN0S2V5d29yZHModG9waWM6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgY29uc3QgcHJvbXB0ID0gYFxuRXh0cmFjdCB0aGUgbW9zdCBpbXBvcnRhbnQga2V5d29yZHMgZnJvbSB0aGlzIHRvcGljIGZvciBHb29nbGUgc2VhcmNoOiBcIiR7dG9waWN9XCJcblxuUmVxdWlyZW1lbnRzOlxuLSBJZiB0aGUgdG9waWMgaXMgYSBzaW5nbGUgd29yZCBvciBzaW1wbGUgcGhyYXNlLCB1c2UgaXQgYXMgdGhlIG1haW4ga2V5d29yZFxuLSBGb3IgY29tcGxleCB0b3BpY3MsIGV4dHJhY3QgMy01IGtleSB0ZXJtcyB0aGF0IGJlc3QgcmVwcmVzZW50IHRoZSB0b3BpY1xuLSBGb2N1cyBvbiB0aGUgbWFpbiBjb25jZXB0cyBhbmQgaW1wb3J0YW50IHRlcm1zXG4tIFVzZSB3b3JkcyB0aGF0IHdvdWxkIGJlIGVmZmVjdGl2ZSBmb3IgR29vZ2xlIHNlYXJjaFxuLSBSZXR1cm4gb25seSB0aGUga2V5d29yZHMgc2VwYXJhdGVkIGJ5IHNwYWNlcywgbm90aGluZyBlbHNlXG4tIERvIG5vdCBpbmNsdWRlIGNvbW1vbiB3b3JkcyBsaWtlIFwidGhlXCIsIFwiYW5kXCIsIFwib2ZcIiwgZXRjLlxuLSBEbyBub3QgYWRkIHdvcmRzIGxpa2UgXCJtZWFuaW5nXCIsIFwiZGVmaW5pdGlvblwiLCBcImV4YW1wbGVcIiB1bmxlc3MgdGhleSBhcmUgcGFydCBvZiB0aGUgb3JpZ2luYWwgdG9waWNcbi0gRm9jdXMgb24gc3BlY2lmaWMsIHNlYXJjaGFibGUgdGVybXMgZnJvbSB0aGUgb3JpZ2luYWwgdG9waWNcblxuRXhhbXBsZXM6XG5Ub3BpYzogXCJtYWdpc3RyYWxcIlxuS2V5d29yZHM6IG1hZ2lzdHJhbFxuXG5Ub3BpYzogXCJIb3cgdG8gYnVpbGQgYSBSZWFjdCBhcHBsaWNhdGlvbiB3aXRoIFR5cGVTY3JpcHRcIlxuS2V5d29yZHM6IFJlYWN0IFR5cGVTY3JpcHQgYXBwbGljYXRpb24gYnVpbGQgZGV2ZWxvcG1lbnRcblxuVG9waWM6IFwiYXJ0aWZpY2lhbCBpbnRlbGxpZ2VuY2UgaW4gaGVhbHRoY2FyZVwiXG5LZXl3b3JkczogYXJ0aWZpY2lhbCBpbnRlbGxpZ2VuY2UgaGVhbHRoY2FyZVxuXG5SZXR1cm4gb25seSB0aGUga2V5d29yZHM6XG5gXG5cbiAgICByZXR1cm4gdGhpcy5nZW5lcmF0ZUNvbnRlbnQocHJvbXB0LCB7XG4gICAgICB0ZW1wZXJhdHVyZTogMC4xLFxuICAgICAgbWF4T3V0cHV0VG9rZW5zOiA1MFxuICAgIH0pXG4gIH1cblxuICBhc3luYyBnZW5lcmF0ZVlvdVR1YmVTY3JpcHQoXG4gICAgdG9waWM6IHN0cmluZyxcbiAgICBkdXJhdGlvbjogc3RyaW5nLFxuICAgIHN0eWxlOiBzdHJpbmcsXG4gICAgdGFyZ2V0QXVkaWVuY2U6IHN0cmluZ1xuICApOiBQcm9taXNlPHN0cmluZz4ge1xuICAgIGNvbnN0IHByb21wdCA9IGBcbkNyZWF0ZSBhIFlvdVR1YmUgdmlkZW8gc2NyaXB0IGFib3V0IFwiJHt0b3BpY31cIi5cblxuVmlkZW8gRHVyYXRpb246ICR7ZHVyYXRpb259XG5TdHlsZTogJHtzdHlsZX1cblRhcmdldCBBdWRpZW5jZTogJHt0YXJnZXRBdWRpZW5jZX1cblxuUmVxdWlyZW1lbnRzOlxuLSBJbmNsdWRlIGNvbXBlbGxpbmcgaG9vayBpbiBmaXJzdCAxNSBzZWNvbmRzXG4tIENsZWFyIHN0cnVjdHVyZSB3aXRoIHRpbWVzdGFtcHNcbi0gRW5nYWdpbmcgc3Rvcnl0ZWxsaW5nIHRocm91Z2hvdXRcbi0gSW5jbHVkZSBjYWxsLXRvLWFjdGlvbiBmb3IgbGlrZXMsIHN1YnNjcmliZXMsIGNvbW1lbnRzXG4tIE5hdHVyYWwgc3BlYWtpbmcgcmh5dGhtIGFuZCBmbG93XG4tIEluY2x1ZGUgY3VlcyBmb3IgdmlzdWFscy9ncmFwaGljcyB3aGVyZSBhcHByb3ByaWF0ZVxuLSBFbmQgd2l0aCBzdHJvbmcgY29uY2x1c2lvbiBhbmQgbmV4dCB2aWRlbyB0ZWFzZXJcblxuRm9ybWF0OlxuW0hPT0sgLSAwOjAwLTA6MTVdXG5bSU5UUk9EVUNUSU9OIC0gMDoxNS0wOjQ1XVxuW01BSU4gQ09OVEVOVCAtIFNlY3Rpb25zIHdpdGggdGltZXN0YW1wc11cbltDT05DTFVTSU9OICYgQ1RBIC0gRmluYWwgc2VjdGlvbl1cblxuQ3JlYXRlIGEgc2NyaXB0IHRoYXQga2VlcHMgdmlld2VycyBlbmdhZ2VkIHRocm91Z2hvdXQgdGhlIGVudGlyZSB2aWRlby5cbmBcblxuICAgIHJldHVybiB0aGlzLmdlbmVyYXRlQ29udGVudChwcm9tcHQsIHsgdGVtcGVyYXR1cmU6IDAuNywgbWF4T3V0cHV0VG9rZW5zOiA1MDAwIH0pXG4gIH1cblxuICBhc3luYyBleHRyYWN0S2V5d29yZHNGcm9tQ29udGVudChjb250ZW50OiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xuICAgIGNvbnN0IHByb21wdCA9IGBcbkFuYWx5emUgdGhpcyBjb250ZW50IGFuZCBleHRyYWN0IHRoZSBtb3N0IGltcG9ydGFudCBTRU8ga2V5d29yZHMgYW5kIHBocmFzZXMgdGhhdCB3b3VsZCBiZSB2YWx1YWJsZSBmb3IgY29udGVudCBvcHRpbWl6YXRpb246XG5cbkNvbnRlbnQ6XG4ke2NvbnRlbnQuc3Vic3RyaW5nKDAsIDMwMDApfVxuXG5SdWxlczpcbi0gRXh0cmFjdCA4LTEyIGhpZ2gtdmFsdWUga2V5d29yZHMgYW5kIHBocmFzZXNcbi0gRm9jdXMgb24gdGVybXMgdGhhdCBhcHBlYXIgZnJlcXVlbnRseSBhbmQgc2VlbSBpbXBvcnRhbnRcbi0gSW5jbHVkZSBib3RoIHNpbmdsZSBrZXl3b3JkcyBhbmQgMi0zIHdvcmQgcGhyYXNlc1xuLSBQcmlvcml0aXplIHRlcm1zIHRoYXQgd291bGQgYmUgZ29vZCBmb3IgU0VPIHRhcmdldGluZ1xuLSBTZXBhcmF0ZSBrZXl3b3JkcyB3aXRoIGNvbW1hc1xuLSBEb24ndCBpbmNsdWRlIGNvbW1vbiB3b3JkcyBsaWtlIFwidGhlXCIsIFwiYW5kXCIsIFwib3JcIiwgZXRjLlxuXG5SZXR1cm4gb25seSB0aGUga2V5d29yZHMgc2VwYXJhdGVkIGJ5IGNvbW1hczpcbmBcblxuICAgIHJldHVybiB0aGlzLmdlbmVyYXRlQ29udGVudChwcm9tcHQsIHtcbiAgICAgIHRlbXBlcmF0dXJlOiAwLjIsXG4gICAgICBtYXhPdXRwdXRUb2tlbnM6IDIwMFxuICAgIH0pXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJHb29nbGVHZW5lcmF0aXZlQUkiLCJnZW5BSSIsInByb2Nlc3MiLCJlbnYiLCJHRU1JTklfQVBJX0tFWSIsIkdlbWluaVNlcnZpY2UiLCJjb25zdHJ1Y3RvciIsIm1vZGVsTmFtZSIsIm1vZGVsIiwiZ2V0R2VuZXJhdGl2ZU1vZGVsIiwiZ2VuZXJhdGVDb250ZW50IiwicHJvbXB0IiwiY29uZmlnIiwiZ2VuZXJhdGlvbkNvbmZpZyIsInRlbXBlcmF0dXJlIiwibWF4T3V0cHV0VG9rZW5zIiwidG9wUCIsInRvcEsiLCJyZXN1bHQiLCJjb250ZW50cyIsInJvbGUiLCJwYXJ0cyIsInRleHQiLCJyZXNwb25zZSIsImVycm9yIiwiY29uc29sZSIsIkVycm9yIiwiZ2VuZXJhdGVCbG9nUG9zdCIsInRvcGljIiwid29yZENvdW50IiwidG9uZSIsInJlc2VhcmNoRGF0YSIsImNvbXBldGl0aW9uRGF0YSIsInRpdGxlIiwidGFyZ2V0S2V5d29yZCIsInRhcmdldEF1ZGllbmNlIiwiY29tcGV0aXRvcnMiLCJnZW5lcmF0ZUVtYWlsIiwicHVycG9zZSIsImF1ZGllbmNlIiwia2V5UG9pbnRzIiwiam9pbiIsImdlbmVyYXRlVHdlZXQiLCJzdHlsZSIsImluY2x1ZGVIYXNodGFncyIsImV4dHJhY3RLZXl3b3JkcyIsImdlbmVyYXRlWW91VHViZVNjcmlwdCIsImR1cmF0aW9uIiwiZXh0cmFjdEtleXdvcmRzRnJvbUNvbnRlbnQiLCJjb250ZW50Iiwic3Vic3RyaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/search.ts":
/*!***************************!*\
  !*** ./src/lib/search.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleSearchService: () => (/* binding */ GoogleSearchService)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var cheerio__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cheerio */ \"(rsc)/./node_modules/cheerio/dist/esm/index.js\");\n\n\nclass GoogleSearchService {\n    constructor(){\n        this.apiKey = \"AIzaSyBlQ7HhWbY37GYbeV9ZJZmTUucspF2KbXE\";\n        this.searchEngineId = \"830840f1a0eaf4acf\";\n    }\n    async search(query, numResults = 10) {\n        try {\n            console.log(`🔍 Searching for: ${query}`);\n            // Check if API keys are configured\n            if (!this.apiKey || !this.searchEngineId) {\n                console.error('❌ Google Search API credentials not configured');\n                throw new Error('Google Search API credentials not configured');\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get('https://www.googleapis.com/customsearch/v1', {\n                params: {\n                    key: this.apiKey,\n                    cx: this.searchEngineId,\n                    q: query,\n                    num: Math.min(numResults, 10)\n                },\n                timeout: 15000\n            });\n            const results = response.data.items?.map((item)=>({\n                    title: item.title,\n                    link: item.link,\n                    snippet: item.snippet,\n                    displayLink: item.displayLink\n                })) || [];\n            console.log(`📊 Found ${results.length} results`);\n            if (results.length === 0) {\n                console.log(`⚠️ No results found for query: \"${query}\"`);\n                console.log(`📊 Total results available: ${response.data.searchInformation?.totalResults || '0'}`);\n            } else {\n                results.forEach((result, index)=>{\n                    console.log(`${index + 1}. ${result.link}`);\n                });\n            }\n            return {\n                items: results,\n                searchInformation: {\n                    totalResults: response.data.searchInformation?.totalResults || '0',\n                    searchTime: response.data.searchInformation?.searchTime || 0\n                }\n            };\n        } catch (error) {\n            console.error('Google Search API error:', error.response?.data || error.message);\n            // Check for specific API errors\n            if (error.response?.status === 403) {\n                console.error('❌ API key invalid or quota exceeded');\n            } else if (error.response?.status === 400) {\n                console.error('❌ Invalid search parameters');\n            }\n            throw new Error(`Failed to perform search: ${error.response?.data?.error?.message || error.message}`);\n        }\n    }\n    async extractContent(url) {\n        try {\n            console.log(`📄 Extracting content from: ${url}`);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(url, {\n                timeout: 10000,\n                headers: {\n                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'\n                }\n            });\n            const $ = cheerio__WEBPACK_IMPORTED_MODULE_0__.load(response.data);\n            // Remove unwanted elements\n            $('script, style, nav, header, footer, aside, .advertisement, .ads, .social-share').remove();\n            // Extract main content\n            let content = '';\n            // Try common content selectors\n            const contentSelectors = [\n                'article',\n                '.content',\n                '.post-content',\n                '.entry-content',\n                '.article-content',\n                'main',\n                '.main-content',\n                '#content',\n                '.post-body',\n                '.article-body'\n            ];\n            for (const selector of contentSelectors){\n                const element = $(selector);\n                if (element.length > 0 && element.text().trim().length > content.length) {\n                    content = element.text().trim();\n                }\n            }\n            // Fallback to body if no content found\n            if (!content) {\n                content = $('body').text().trim();\n            }\n            // Clean up the content\n            content = content.replace(/\\s+/g, ' ').replace(/\\n\\s*\\n/g, '\\n').trim();\n            console.log(`✅ Extracted ${content.length} characters from ${url}`);\n            return content;\n        } catch (error) {\n            console.error(`❌ Failed to extract content from ${url}:`, error);\n            return '';\n        }\n    }\n    async searchAndExtract(query, numResults = 5) {\n        try {\n            // Perform search\n            const searchResponse = await this.search(query, numResults);\n            // Extract content from URLs in parallel\n            const extractionPromises = searchResponse.items.map(async (result)=>({\n                    url: result.link,\n                    content: await this.extractContent(result.link)\n                }));\n            const extractedContent = await Promise.all(extractionPromises);\n            // Filter out empty content\n            const validContent = extractedContent.filter((item)=>item.content.length > 100);\n            console.log(`📚 Successfully extracted content from ${validContent.length}/${searchResponse.items.length} URLs`);\n            return {\n                searchResults: searchResponse.items,\n                extractedContent: validContent\n            };\n        } catch (error) {\n            console.error('Search and extract error:', error);\n            throw new Error('Failed to search and extract content');\n        }\n    }\n    formatResearchData(extractedContent) {\n        return extractedContent.map((item, index)=>{\n            const truncatedContent = item.content.length > 2000 ? item.content.substring(0, 2000) + '...' : item.content;\n            return `=== SOURCE ${index + 1}: ${item.url} ===\\n${truncatedContent}\\n`;\n        }).join('\\n');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/search.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:assert":
/*!******************************!*\
  !*** external "node:assert" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:assert");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:console");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:dns":
/*!***************************!*\
  !*** external "node:dns" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:dns");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:http2":
/*!*****************************!*\
  !*** external "node:http2" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http2");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:perf_hooks":
/*!**********************************!*\
  !*** external "node:perf_hooks" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:perf_hooks");

/***/ }),

/***/ "node:querystring":
/*!***********************************!*\
  !*** external "node:querystring" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:querystring");

/***/ }),

/***/ "node:sqlite":
/*!******************************!*\
  !*** external "node:sqlite" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:sqlite");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:util/types":
/*!**********************************!*\
  !*** external "node:util/types" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util/types");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/eventemitter3","vendor-chunks/undici","vendor-chunks/zod","vendor-chunks/@langchain","vendor-chunks/axios","vendor-chunks/semver","vendor-chunks/zod-to-json-schema","vendor-chunks/langsmith","vendor-chunks/iconv-lite","vendor-chunks/parse5","vendor-chunks/cheerio","vendor-chunks/kaibanjs","vendor-chunks/css-select","vendor-chunks/asynckit","vendor-chunks/@cfworker","vendor-chunks/entities","vendor-chunks/domutils","vendor-chunks/math-intrinsics","vendor-chunks/htmlparser2","vendor-chunks/es-errors","vendor-chunks/whatwg-mimetype","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/nth-check","vendor-chunks/cheerio-select","vendor-chunks/whatwg-encoding","vendor-chunks/retry","vendor-chunks/p-queue","vendor-chunks/get-proto","vendor-chunks/js-tiktoken","vendor-chunks/encoding-sniffer","vendor-chunks/domhandler","vendor-chunks/dom-serializer","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/css-what","vendor-chunks/parse5-parser-stream","vendor-chunks/parse5-htmlparser2-tree-adapter","vendor-chunks/domelementtype","vendor-chunks/@google","vendor-chunks/supports-color","vendor-chunks/safer-buffer","vendor-chunks/proxy-from-env","vendor-chunks/p-timeout","vendor-chunks/p-retry","vendor-chunks/p-finally","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/decamelize","vendor-chunks/combined-stream","vendor-chunks/camelcase","vendor-chunks/boolbase","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-superagent%2Froute&page=%2Fapi%2Fkaiban-superagent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-superagent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();