"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/encoding-sniffer";
exports.ids = ["vendor-chunks/encoding-sniffer"];
exports.modules = {

/***/ "(rsc)/./node_modules/encoding-sniffer/dist/esm/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/encoding-sniffer/dist/esm/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DecodeStream: () => (/* binding */ DecodeStream),\n/* harmony export */   decodeBuffer: () => (/* binding */ decodeBuffer),\n/* harmony export */   getEncoding: () => (/* reexport safe */ _sniffer_js__WEBPACK_IMPORTED_MODULE_2__.getEncoding)\n/* harmony export */ });\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var iconv_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! iconv-lite */ \"(rsc)/./node_modules/iconv-lite/lib/index.js\");\n/* harmony import */ var _sniffer_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sniffer.js */ \"(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js\");\n\n\n\n/**\n * Sniff the encoding of a buffer, then decode it.\n *\n * @param buffer Buffer to be decoded\n * @param options Options for the sniffer\n * @returns The decoded buffer\n */\nfunction decodeBuffer(buffer, options = {}) {\n    return iconv_lite__WEBPACK_IMPORTED_MODULE_1__.decode(buffer, (0,_sniffer_js__WEBPACK_IMPORTED_MODULE_2__.getEncoding)(buffer, options));\n}\n/**\n * Decodes a stream of buffers into a stream of strings.\n *\n * Reads the first 1024 bytes and passes them to the sniffer. Once an encoding\n * has been determined, it passes all data to iconv-lite's stream and outputs\n * the results.\n */\nclass DecodeStream extends node_stream__WEBPACK_IMPORTED_MODULE_0__.Transform {\n    constructor(options) {\n        var _a;\n        super({ decodeStrings: false, encoding: \"utf-8\" });\n        this.buffers = [];\n        /** The iconv decode stream. If it is set, we have read more than `options.maxBytes` bytes. */\n        this.iconv = null;\n        this.readBytes = 0;\n        this.sniffer = new _sniffer_js__WEBPACK_IMPORTED_MODULE_2__.Sniffer(options);\n        this.maxBytes = (_a = options === null || options === void 0 ? void 0 : options.maxBytes) !== null && _a !== void 0 ? _a : 1024;\n    }\n    _transform(chunk, _encoding, callback) {\n        if (this.readBytes < this.maxBytes) {\n            this.sniffer.write(chunk);\n            this.readBytes += chunk.length;\n            if (this.readBytes < this.maxBytes) {\n                this.buffers.push(chunk);\n                callback();\n                return;\n            }\n        }\n        this.getIconvStream().write(chunk, callback);\n    }\n    getIconvStream() {\n        if (this.iconv) {\n            return this.iconv;\n        }\n        const stream = iconv_lite__WEBPACK_IMPORTED_MODULE_1__.decodeStream(this.sniffer.encoding);\n        stream.on(\"data\", (chunk) => this.push(chunk, \"utf-8\"));\n        stream.on(\"end\", () => this.push(null));\n        this.iconv = stream;\n        for (const buffer of this.buffers) {\n            stream.write(buffer);\n        }\n        this.buffers.length = 0;\n        return stream;\n    }\n    _flush(callback) {\n        this.getIconvStream().end(callback);\n    }\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZW5jb2Rpbmctc25pZmZlci9kaXN0L2VzbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBd0M7QUFDVDtBQUNxQjtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLDBDQUEwQztBQUNqRCxXQUFXLDhDQUFZLFNBQVMsd0RBQVc7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLDJCQUEyQixrREFBUztBQUMzQztBQUNBO0FBQ0EsZ0JBQWdCLHlDQUF5QztBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixnREFBTztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsb0RBQWtCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQzJDO0FBQzNDIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvbm9kZV9tb2R1bGVzL2VuY29kaW5nLXNuaWZmZXIvZGlzdC9lc20vaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgVHJhbnNmb3JtIH0gZnJvbSBcIm5vZGU6c3RyZWFtXCI7XG5pbXBvcnQgaWNvbnYgZnJvbSBcImljb252LWxpdGVcIjtcbmltcG9ydCB7IFNuaWZmZXIsIGdldEVuY29kaW5nIH0gZnJvbSBcIi4vc25pZmZlci5qc1wiO1xuLyoqXG4gKiBTbmlmZiB0aGUgZW5jb2Rpbmcgb2YgYSBidWZmZXIsIHRoZW4gZGVjb2RlIGl0LlxuICpcbiAqIEBwYXJhbSBidWZmZXIgQnVmZmVyIHRvIGJlIGRlY29kZWRcbiAqIEBwYXJhbSBvcHRpb25zIE9wdGlvbnMgZm9yIHRoZSBzbmlmZmVyXG4gKiBAcmV0dXJucyBUaGUgZGVjb2RlZCBidWZmZXJcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlY29kZUJ1ZmZlcihidWZmZXIsIG9wdGlvbnMgPSB7fSkge1xuICAgIHJldHVybiBpY29udi5kZWNvZGUoYnVmZmVyLCBnZXRFbmNvZGluZyhidWZmZXIsIG9wdGlvbnMpKTtcbn1cbi8qKlxuICogRGVjb2RlcyBhIHN0cmVhbSBvZiBidWZmZXJzIGludG8gYSBzdHJlYW0gb2Ygc3RyaW5ncy5cbiAqXG4gKiBSZWFkcyB0aGUgZmlyc3QgMTAyNCBieXRlcyBhbmQgcGFzc2VzIHRoZW0gdG8gdGhlIHNuaWZmZXIuIE9uY2UgYW4gZW5jb2RpbmdcbiAqIGhhcyBiZWVuIGRldGVybWluZWQsIGl0IHBhc3NlcyBhbGwgZGF0YSB0byBpY29udi1saXRlJ3Mgc3RyZWFtIGFuZCBvdXRwdXRzXG4gKiB0aGUgcmVzdWx0cy5cbiAqL1xuZXhwb3J0IGNsYXNzIERlY29kZVN0cmVhbSBleHRlbmRzIFRyYW5zZm9ybSB7XG4gICAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIHN1cGVyKHsgZGVjb2RlU3RyaW5nczogZmFsc2UsIGVuY29kaW5nOiBcInV0Zi04XCIgfSk7XG4gICAgICAgIHRoaXMuYnVmZmVycyA9IFtdO1xuICAgICAgICAvKiogVGhlIGljb252IGRlY29kZSBzdHJlYW0uIElmIGl0IGlzIHNldCwgd2UgaGF2ZSByZWFkIG1vcmUgdGhhbiBgb3B0aW9ucy5tYXhCeXRlc2AgYnl0ZXMuICovXG4gICAgICAgIHRoaXMuaWNvbnYgPSBudWxsO1xuICAgICAgICB0aGlzLnJlYWRCeXRlcyA9IDA7XG4gICAgICAgIHRoaXMuc25pZmZlciA9IG5ldyBTbmlmZmVyKG9wdGlvbnMpO1xuICAgICAgICB0aGlzLm1heEJ5dGVzID0gKF9hID0gb3B0aW9ucyA9PT0gbnVsbCB8fCBvcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRpb25zLm1heEJ5dGVzKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiAxMDI0O1xuICAgIH1cbiAgICBfdHJhbnNmb3JtKGNodW5rLCBfZW5jb2RpbmcsIGNhbGxiYWNrKSB7XG4gICAgICAgIGlmICh0aGlzLnJlYWRCeXRlcyA8IHRoaXMubWF4Qnl0ZXMpIHtcbiAgICAgICAgICAgIHRoaXMuc25pZmZlci53cml0ZShjaHVuayk7XG4gICAgICAgICAgICB0aGlzLnJlYWRCeXRlcyArPSBjaHVuay5sZW5ndGg7XG4gICAgICAgICAgICBpZiAodGhpcy5yZWFkQnl0ZXMgPCB0aGlzLm1heEJ5dGVzKSB7XG4gICAgICAgICAgICAgICAgdGhpcy5idWZmZXJzLnB1c2goY2h1bmspO1xuICAgICAgICAgICAgICAgIGNhbGxiYWNrKCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRoaXMuZ2V0SWNvbnZTdHJlYW0oKS53cml0ZShjaHVuaywgY2FsbGJhY2spO1xuICAgIH1cbiAgICBnZXRJY29udlN0cmVhbSgpIHtcbiAgICAgICAgaWYgKHRoaXMuaWNvbnYpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmljb252O1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHN0cmVhbSA9IGljb252LmRlY29kZVN0cmVhbSh0aGlzLnNuaWZmZXIuZW5jb2RpbmcpO1xuICAgICAgICBzdHJlYW0ub24oXCJkYXRhXCIsIChjaHVuaykgPT4gdGhpcy5wdXNoKGNodW5rLCBcInV0Zi04XCIpKTtcbiAgICAgICAgc3RyZWFtLm9uKFwiZW5kXCIsICgpID0+IHRoaXMucHVzaChudWxsKSk7XG4gICAgICAgIHRoaXMuaWNvbnYgPSBzdHJlYW07XG4gICAgICAgIGZvciAoY29uc3QgYnVmZmVyIG9mIHRoaXMuYnVmZmVycykge1xuICAgICAgICAgICAgc3RyZWFtLndyaXRlKGJ1ZmZlcik7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5idWZmZXJzLmxlbmd0aCA9IDA7XG4gICAgICAgIHJldHVybiBzdHJlYW07XG4gICAgfVxuICAgIF9mbHVzaChjYWxsYmFjaykge1xuICAgICAgICB0aGlzLmdldEljb252U3RyZWFtKCkuZW5kKGNhbGxiYWNrKTtcbiAgICB9XG59XG5leHBvcnQgeyBnZXRFbmNvZGluZyB9IGZyb20gXCIuL3NuaWZmZXIuanNcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/encoding-sniffer/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js":
/*!***********************************************************!*\
  !*** ./node_modules/encoding-sniffer/dist/esm/sniffer.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResultType: () => (/* binding */ ResultType),\n/* harmony export */   STRINGS: () => (/* binding */ STRINGS),\n/* harmony export */   Sniffer: () => (/* binding */ Sniffer),\n/* harmony export */   getEncoding: () => (/* binding */ getEncoding)\n/* harmony export */ });\n/* harmony import */ var whatwg_encoding__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! whatwg-encoding */ \"(rsc)/./node_modules/whatwg-encoding/lib/whatwg-encoding.js\");\n\n// https://html.spec.whatwg.org/multipage/syntax.html#prescan-a-byte-stream-to-determine-its-encoding\nvar State;\n(function (State) {\n    // Before anything starts; can be any of BOM, UTF-16 XML declarations or meta tags\n    State[State[\"Begin\"] = 0] = \"Begin\";\n    // Inside of a BOM\n    State[State[\"BOM16BE\"] = 1] = \"BOM16BE\";\n    State[State[\"BOM16LE\"] = 2] = \"BOM16LE\";\n    State[State[\"BOM8\"] = 3] = \"BOM8\";\n    // XML prefix\n    State[State[\"UTF16LE_XML_PREFIX\"] = 4] = \"UTF16LE_XML_PREFIX\";\n    State[State[\"BeginLT\"] = 5] = \"BeginLT\";\n    State[State[\"UTF16BE_XML_PREFIX\"] = 6] = \"UTF16BE_XML_PREFIX\";\n    // Waiting for opening `<`\n    State[State[\"BeforeTag\"] = 7] = \"BeforeTag\";\n    // After the opening `<`\n    State[State[\"BeforeTagName\"] = 8] = \"BeforeTagName\";\n    // After `</`\n    State[State[\"BeforeCloseTagName\"] = 9] = \"BeforeCloseTagName\";\n    // Beginning of a comment\n    State[State[\"CommentStart\"] = 10] = \"CommentStart\";\n    // End of a comment\n    State[State[\"CommentEnd\"] = 11] = \"CommentEnd\";\n    // A tag name that could be `meta`\n    State[State[\"TagNameMeta\"] = 12] = \"TagNameMeta\";\n    // A tag name that is not `meta`\n    State[State[\"TagNameOther\"] = 13] = \"TagNameOther\";\n    // XML declaration\n    State[State[\"XMLDeclaration\"] = 14] = \"XMLDeclaration\";\n    State[State[\"XMLDeclarationBeforeEncoding\"] = 15] = \"XMLDeclarationBeforeEncoding\";\n    State[State[\"XMLDeclarationAfterEncoding\"] = 16] = \"XMLDeclarationAfterEncoding\";\n    State[State[\"XMLDeclarationBeforeValue\"] = 17] = \"XMLDeclarationBeforeValue\";\n    State[State[\"XMLDeclarationValue\"] = 18] = \"XMLDeclarationValue\";\n    // Anything that looks like a tag, but doesn't fit in the above categories\n    State[State[\"WeirdTag\"] = 19] = \"WeirdTag\";\n    State[State[\"BeforeAttribute\"] = 20] = \"BeforeAttribute\";\n    /*\n     * Attributes in meta tag — we compare them to our set here, and back out\n     * We care about four attributes: http-equiv, content-type, content, charset\n     */\n    State[State[\"MetaAttribHttpEquiv\"] = 21] = \"MetaAttribHttpEquiv\";\n    // The value has to be `content-type`\n    State[State[\"MetaAttribHttpEquivValue\"] = 22] = \"MetaAttribHttpEquivValue\";\n    State[State[\"MetaAttribC\"] = 23] = \"MetaAttribC\";\n    State[State[\"MetaAttribContent\"] = 24] = \"MetaAttribContent\";\n    State[State[\"MetaAttribCharset\"] = 25] = \"MetaAttribCharset\";\n    // Waiting for whitespace\n    State[State[\"MetaAttribAfterName\"] = 26] = \"MetaAttribAfterName\";\n    State[State[\"MetaContentValueQuotedBeforeEncoding\"] = 27] = \"MetaContentValueQuotedBeforeEncoding\";\n    State[State[\"MetaContentValueQuotedAfterEncoding\"] = 28] = \"MetaContentValueQuotedAfterEncoding\";\n    State[State[\"MetaContentValueQuotedBeforeValue\"] = 29] = \"MetaContentValueQuotedBeforeValue\";\n    State[State[\"MetaContentValueQuotedValueQuoted\"] = 30] = \"MetaContentValueQuotedValueQuoted\";\n    State[State[\"MetaContentValueQuotedValueUnquoted\"] = 31] = \"MetaContentValueQuotedValueUnquoted\";\n    State[State[\"MetaContentValueUnquotedBeforeEncoding\"] = 32] = \"MetaContentValueUnquotedBeforeEncoding\";\n    State[State[\"MetaContentValueUnquotedBeforeValue\"] = 33] = \"MetaContentValueUnquotedBeforeValue\";\n    State[State[\"MetaContentValueUnquotedValueQuoted\"] = 34] = \"MetaContentValueUnquotedValueQuoted\";\n    State[State[\"MetaContentValueUnquotedValueUnquoted\"] = 35] = \"MetaContentValueUnquotedValueUnquoted\";\n    State[State[\"AnyAttribName\"] = 36] = \"AnyAttribName\";\n    // After the name of an attribute, before the equals sign\n    State[State[\"AfterAttributeName\"] = 37] = \"AfterAttributeName\";\n    // After `=`\n    State[State[\"BeforeAttributeValue\"] = 38] = \"BeforeAttributeValue\";\n    State[State[\"AttributeValueQuoted\"] = 39] = \"AttributeValueQuoted\";\n    State[State[\"AttributeValueUnquoted\"] = 40] = \"AttributeValueUnquoted\";\n})(State || (State = {}));\nvar ResultType;\n(function (ResultType) {\n    // Byte order mark\n    ResultType[ResultType[\"BOM\"] = 0] = \"BOM\";\n    // User- or transport layer-defined\n    ResultType[ResultType[\"PASSED\"] = 1] = \"PASSED\";\n    // XML prefixes\n    ResultType[ResultType[\"XML_PREFIX\"] = 2] = \"XML_PREFIX\";\n    // Meta tag\n    ResultType[ResultType[\"META_TAG\"] = 3] = \"META_TAG\";\n    // XML encoding\n    ResultType[ResultType[\"XML_ENCODING\"] = 4] = \"XML_ENCODING\";\n    // Default\n    ResultType[ResultType[\"DEFAULT\"] = 5] = \"DEFAULT\";\n})(ResultType || (ResultType = {}));\nvar AttribType;\n(function (AttribType) {\n    AttribType[AttribType[\"None\"] = 0] = \"None\";\n    AttribType[AttribType[\"HttpEquiv\"] = 1] = \"HttpEquiv\";\n    AttribType[AttribType[\"Content\"] = 2] = \"Content\";\n    AttribType[AttribType[\"Charset\"] = 3] = \"Charset\";\n})(AttribType || (AttribType = {}));\nvar Chars;\n(function (Chars) {\n    Chars[Chars[\"NIL\"] = 0] = \"NIL\";\n    Chars[Chars[\"TAB\"] = 9] = \"TAB\";\n    Chars[Chars[\"LF\"] = 10] = \"LF\";\n    Chars[Chars[\"CR\"] = 13] = \"CR\";\n    Chars[Chars[\"SPACE\"] = 32] = \"SPACE\";\n    Chars[Chars[\"EXCLAMATION\"] = 33] = \"EXCLAMATION\";\n    Chars[Chars[\"DQUOTE\"] = 34] = \"DQUOTE\";\n    Chars[Chars[\"SQUOTE\"] = 39] = \"SQUOTE\";\n    Chars[Chars[\"DASH\"] = 45] = \"DASH\";\n    Chars[Chars[\"SLASH\"] = 47] = \"SLASH\";\n    Chars[Chars[\"SEMICOLON\"] = 59] = \"SEMICOLON\";\n    Chars[Chars[\"LT\"] = 60] = \"LT\";\n    Chars[Chars[\"EQUALS\"] = 61] = \"EQUALS\";\n    Chars[Chars[\"GT\"] = 62] = \"GT\";\n    Chars[Chars[\"QUESTION\"] = 63] = \"QUESTION\";\n    Chars[Chars[\"UpperA\"] = 65] = \"UpperA\";\n    Chars[Chars[\"UpperZ\"] = 90] = \"UpperZ\";\n    Chars[Chars[\"LowerA\"] = 97] = \"LowerA\";\n    Chars[Chars[\"LowerZ\"] = 122] = \"LowerZ\";\n})(Chars || (Chars = {}));\nconst SPACE_CHARACTERS = new Set([Chars.SPACE, Chars.LF, Chars.CR, Chars.TAB]);\nconst END_OF_UNQUOTED_ATTRIBUTE_VALUE = new Set([\n    Chars.SPACE,\n    Chars.LF,\n    Chars.CR,\n    Chars.TAB,\n    Chars.GT,\n]);\nfunction toUint8Array(str) {\n    const arr = new Uint8Array(str.length);\n    for (let i = 0; i < str.length; i++) {\n        arr[i] = str.charCodeAt(i);\n    }\n    return arr;\n}\nconst STRINGS = {\n    UTF8_BOM: new Uint8Array([0xef, 0xbb, 0xbf]),\n    UTF16LE_BOM: new Uint8Array([0xff, 0xfe]),\n    UTF16BE_BOM: new Uint8Array([0xfe, 0xff]),\n    UTF16LE_XML_PREFIX: new Uint8Array([0x3c, 0x0, 0x3f, 0x0, 0x78, 0x0]),\n    UTF16BE_XML_PREFIX: new Uint8Array([0x0, 0x3c, 0x0, 0x3f, 0x0, 0x78]),\n    XML_DECLARATION: toUint8Array(\"<?xml\"),\n    ENCODING: toUint8Array(\"encoding\"),\n    META: toUint8Array(\"meta\"),\n    HTTP_EQUIV: toUint8Array(\"http-equiv\"),\n    CONTENT: toUint8Array(\"content\"),\n    CONTENT_TYPE: toUint8Array(\"content-type\"),\n    CHARSET: toUint8Array(\"charset\"),\n    COMMENT_START: toUint8Array(\"<!--\"),\n    COMMENT_END: toUint8Array(\"-->\"),\n};\nfunction isAsciiAlpha(c) {\n    return ((c >= Chars.UpperA && c <= Chars.UpperZ) ||\n        (c >= Chars.LowerA && c <= Chars.LowerZ));\n}\nfunction isQuote(c) {\n    return c === Chars.DQUOTE || c === Chars.SQUOTE;\n}\nclass Sniffer {\n    setResult(label, type) {\n        if (this.resultType === ResultType.DEFAULT || this.resultType > type) {\n            const encoding = (0,whatwg_encoding__WEBPACK_IMPORTED_MODULE_0__.labelToName)(label);\n            if (encoding) {\n                this.encoding =\n                    // Check if we are in a meta tag and the encoding is `x-user-defined`\n                    type === ResultType.META_TAG &&\n                        encoding === \"x-user-defined\"\n                        ? \"windows-1252\"\n                        : // Check if we are in a meta tag or xml declaration, and the encoding is UTF-16\n                            (type === ResultType.META_TAG ||\n                                type === ResultType.XML_ENCODING) &&\n                                (encoding === \"UTF-16LE\" || encoding === \"UTF-16BE\")\n                                ? \"UTF-8\"\n                                : encoding;\n                this.resultType = type;\n            }\n        }\n    }\n    constructor({ maxBytes = 1024, userEncoding, transportLayerEncodingLabel, defaultEncoding, } = {}) {\n        /** The offset of the previous buffers. */\n        this.offset = 0;\n        this.state = State.Begin;\n        this.sectionIndex = 0;\n        this.attribType = AttribType.None;\n        /**\n         * Indicates if the `http-equiv` is `content-type`.\n         *\n         * Initially `null`, a boolean when a value is found.\n         */\n        this.gotPragma = null;\n        this.needsPragma = null;\n        this.inMetaTag = false;\n        this.encoding = \"windows-1252\";\n        this.resultType = ResultType.DEFAULT;\n        this.quoteCharacter = 0;\n        this.attributeValue = [];\n        this.maxBytes = maxBytes;\n        if (userEncoding) {\n            this.setResult(userEncoding, ResultType.PASSED);\n        }\n        if (transportLayerEncodingLabel) {\n            this.setResult(transportLayerEncodingLabel, ResultType.PASSED);\n        }\n        if (defaultEncoding) {\n            this.setResult(defaultEncoding, ResultType.DEFAULT);\n        }\n    }\n    stateBegin(c) {\n        switch (c) {\n            case STRINGS.UTF16BE_BOM[0]: {\n                this.state = State.BOM16BE;\n                break;\n            }\n            case STRINGS.UTF16LE_BOM[0]: {\n                this.state = State.BOM16LE;\n                break;\n            }\n            case STRINGS.UTF8_BOM[0]: {\n                this.sectionIndex = 1;\n                this.state = State.BOM8;\n                break;\n            }\n            case Chars.NIL: {\n                this.state = State.UTF16BE_XML_PREFIX;\n                this.sectionIndex = 1;\n                break;\n            }\n            case Chars.LT: {\n                this.state = State.BeginLT;\n                break;\n            }\n            default: {\n                this.state = State.BeforeTag;\n            }\n        }\n    }\n    stateBeginLT(c) {\n        if (c === Chars.NIL) {\n            this.state = State.UTF16LE_XML_PREFIX;\n            this.sectionIndex = 2;\n        }\n        else if (c === Chars.QUESTION) {\n            this.state = State.XMLDeclaration;\n            this.sectionIndex = 2;\n        }\n        else {\n            this.state = State.BeforeTagName;\n            this.stateBeforeTagName(c);\n        }\n    }\n    stateUTF16BE_XML_PREFIX(c) {\n        // Advance position in the section\n        if (this.advanceSection(STRINGS.UTF16BE_XML_PREFIX, c)) {\n            if (this.sectionIndex === STRINGS.UTF16BE_XML_PREFIX.length) {\n                // We have the whole prefix\n                this.setResult(\"utf-16be\", ResultType.XML_PREFIX);\n            }\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateUTF16LE_XML_PREFIX(c) {\n        // Advance position in the section\n        if (this.advanceSection(STRINGS.UTF16LE_XML_PREFIX, c)) {\n            if (this.sectionIndex === STRINGS.UTF16LE_XML_PREFIX.length) {\n                // We have the whole prefix\n                this.setResult(\"utf-16le\", ResultType.XML_PREFIX);\n            }\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateBOM16LE(c) {\n        if (c === STRINGS.UTF16LE_BOM[1]) {\n            this.setResult(\"utf-16le\", ResultType.BOM);\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateBOM16BE(c) {\n        if (c === STRINGS.UTF16BE_BOM[1]) {\n            this.setResult(\"utf-16be\", ResultType.BOM);\n        }\n        else {\n            this.state = State.BeforeTag;\n            this.stateBeforeTag(c);\n        }\n    }\n    stateBOM8(c) {\n        if (this.advanceSection(STRINGS.UTF8_BOM, c) &&\n            this.sectionIndex === STRINGS.UTF8_BOM.length) {\n            this.setResult(\"utf-8\", ResultType.BOM);\n        }\n    }\n    stateBeforeTag(c) {\n        if (c === Chars.LT) {\n            this.state = State.BeforeTagName;\n            this.inMetaTag = false;\n        }\n    }\n    /**\n     * We have seen a `<`, and now have to figure out what to do.\n     *\n     * Options:\n     *  - `<meta`\n     *  - Any other tag\n     *  - A closing tag\n     *  - `<!--`\n     *  - An XML declaration\n     *\n     */\n    stateBeforeTagName(c) {\n        if (isAsciiAlpha(c)) {\n            if ((c | 0x20) === STRINGS.META[0]) {\n                this.sectionIndex = 1;\n                this.state = State.TagNameMeta;\n            }\n            else {\n                this.state = State.TagNameOther;\n            }\n        }\n        else\n            switch (c) {\n                case Chars.SLASH: {\n                    this.state = State.BeforeCloseTagName;\n                    break;\n                }\n                case Chars.EXCLAMATION: {\n                    this.state = State.CommentStart;\n                    this.sectionIndex = 2;\n                    break;\n                }\n                case Chars.QUESTION: {\n                    this.state = State.WeirdTag;\n                    break;\n                }\n                default: {\n                    this.state = State.BeforeTag;\n                    this.stateBeforeTag(c);\n                }\n            }\n    }\n    stateBeforeCloseTagName(c) {\n        this.state = isAsciiAlpha(c)\n            ? // Switch to `TagNameOther`; the HTML spec allows attributes here as well.\n                State.TagNameOther\n            : State.WeirdTag;\n    }\n    stateCommentStart(c) {\n        if (this.advanceSection(STRINGS.COMMENT_START, c)) {\n            if (this.sectionIndex === STRINGS.COMMENT_START.length) {\n                this.state = State.CommentEnd;\n                // The -- of the comment start can be part of the end.\n                this.sectionIndex = 2;\n            }\n        }\n        else {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    }\n    stateCommentEnd(c) {\n        if (this.advanceSection(STRINGS.COMMENT_END, c)) {\n            if (this.sectionIndex === STRINGS.COMMENT_END.length) {\n                this.state = State.BeforeTag;\n            }\n        }\n        else if (c === Chars.DASH) {\n            /*\n             * If we are here, we know we expected a `>` above.\n             * Set this to 2, to support many dashes before the closing `>`.\n             */\n            this.sectionIndex = 2;\n        }\n    }\n    /**\n     * Any section starting with `<!`, `<?`, `</`, without being a closing tag or comment.\n     */\n    stateWeirdTag(c) {\n        if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n    }\n    /**\n     * Advances the section, ignoring upper/lower case.\n     *\n     * Make sure the section has left-over characters before calling.\n     *\n     * @returns `false` if we did not match the section.\n     */\n    advanceSectionIC(section, c) {\n        return this.advanceSection(section, c | 0x20);\n    }\n    /**\n     * Advances the section.\n     *\n     * Make sure the section has left-over characters before calling.\n     *\n     * @returns `false` if we did not match the section.\n     */\n    advanceSection(section, c) {\n        if (section[this.sectionIndex] === c) {\n            this.sectionIndex++;\n            return true;\n        }\n        this.sectionIndex = 0;\n        return false;\n    }\n    stateTagNameMeta(c) {\n        if (this.sectionIndex < STRINGS.META.length) {\n            if (this.advanceSectionIC(STRINGS.META, c)) {\n                return;\n            }\n        }\n        else if (SPACE_CHARACTERS.has(c)) {\n            this.inMetaTag = true;\n            this.gotPragma = null;\n            this.needsPragma = null;\n            this.state = State.BeforeAttribute;\n            return;\n        }\n        this.state = State.TagNameOther;\n        // Reconsume in case there is a `>`.\n        this.stateTagNameOther(c);\n    }\n    stateTagNameOther(c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.state = State.BeforeAttribute;\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n    }\n    stateBeforeAttribute(c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        if (this.inMetaTag) {\n            const lower = c | 0x20;\n            if (lower === STRINGS.HTTP_EQUIV[0]) {\n                this.sectionIndex = 1;\n                this.state = State.MetaAttribHttpEquiv;\n                return;\n            }\n            else if (lower === STRINGS.CHARSET[0]) {\n                this.sectionIndex = 1;\n                this.state = State.MetaAttribC;\n                return;\n            }\n        }\n        this.state =\n            c === Chars.SLASH || c === Chars.GT\n                ? State.BeforeTag\n                : State.AnyAttribName;\n    }\n    handleMetaAttrib(c, section, type) {\n        if (this.advanceSectionIC(section, c)) {\n            if (this.sectionIndex === section.length) {\n                this.attribType = type;\n                this.state = State.MetaAttribAfterName;\n            }\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    }\n    stateMetaAttribHttpEquiv(c) {\n        this.handleMetaAttrib(c, STRINGS.HTTP_EQUIV, AttribType.HttpEquiv);\n    }\n    stateMetaAttribC(c) {\n        const lower = c | 0x20;\n        if (lower === STRINGS.CHARSET[1]) {\n            this.sectionIndex = 2;\n            this.state = State.MetaAttribCharset;\n        }\n        else if (lower === STRINGS.CONTENT[1]) {\n            this.sectionIndex = 2;\n            this.state = State.MetaAttribContent;\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    }\n    stateMetaAttribCharset(c) {\n        this.handleMetaAttrib(c, STRINGS.CHARSET, AttribType.Charset);\n    }\n    stateMetaAttribContent(c) {\n        this.handleMetaAttrib(c, STRINGS.CONTENT, AttribType.Content);\n    }\n    stateMetaAttribAfterName(c) {\n        if (SPACE_CHARACTERS.has(c) || c === Chars.EQUALS) {\n            this.state = State.AfterAttributeName;\n            this.stateAfterAttributeName(c);\n        }\n        else {\n            this.state = State.AnyAttribName;\n            this.stateAnyAttribName(c);\n        }\n    }\n    stateAnyAttribName(c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.attribType = AttribType.None;\n            this.state = State.AfterAttributeName;\n        }\n        else if (c === Chars.SLASH || c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else if (c === Chars.EQUALS) {\n            this.state = State.BeforeAttributeValue;\n        }\n    }\n    stateAfterAttributeName(c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        if (c === Chars.EQUALS) {\n            this.state = State.BeforeAttributeValue;\n        }\n        else {\n            this.state = State.BeforeAttribute;\n            this.stateBeforeAttribute(c);\n        }\n    }\n    stateBeforeAttributeValue(c) {\n        if (SPACE_CHARACTERS.has(c))\n            return;\n        this.attributeValue.length = 0;\n        this.sectionIndex = 0;\n        if (isQuote(c)) {\n            this.quoteCharacter = c;\n            this.state =\n                this.attribType === AttribType.Content\n                    ? State.MetaContentValueQuotedBeforeEncoding\n                    : this.attribType === AttribType.HttpEquiv\n                        ? State.MetaAttribHttpEquivValue\n                        : State.AttributeValueQuoted;\n        }\n        else if (this.attribType === AttribType.Content) {\n            this.state = State.MetaContentValueUnquotedBeforeEncoding;\n            this.stateMetaContentValueUnquotedBeforeEncoding(c);\n        }\n        else if (this.attribType === AttribType.HttpEquiv) {\n            // We use `quoteCharacter = 0` to signify that the value is unquoted.\n            this.quoteCharacter = 0;\n            this.sectionIndex = 0;\n            this.state = State.MetaAttribHttpEquivValue;\n            this.stateMetaAttribHttpEquivValue(c);\n        }\n        else {\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n    }\n    // The value has to be `content-type`\n    stateMetaAttribHttpEquivValue(c) {\n        if (this.sectionIndex === STRINGS.CONTENT_TYPE.length) {\n            if (this.quoteCharacter === 0\n                ? END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)\n                : c === this.quoteCharacter) {\n                if (this.needsPragma !== null) {\n                    this.setResult(this.needsPragma, ResultType.META_TAG);\n                }\n                else if (this.gotPragma === null) {\n                    this.gotPragma = true;\n                }\n                this.state = State.BeforeAttribute;\n                return;\n            }\n        }\n        else if (this.advanceSectionIC(STRINGS.CONTENT_TYPE, c)) {\n            return;\n        }\n        this.gotPragma = false;\n        if (this.quoteCharacter === 0) {\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n    }\n    handleMetaContentValue() {\n        if (this.attributeValue.length === 0)\n            return;\n        const encoding = String.fromCharCode(...this.attributeValue);\n        if (this.gotPragma) {\n            this.setResult(encoding, ResultType.META_TAG);\n        }\n        else if (this.needsPragma === null) {\n            // Don't override a previous result.\n            this.needsPragma = encoding;\n        }\n        this.attributeValue.length = 0;\n    }\n    handleAttributeValue() {\n        if (this.attribType === AttribType.Charset) {\n            this.setResult(String.fromCharCode(...this.attributeValue), ResultType.META_TAG);\n        }\n    }\n    stateAttributeValueUnquoted(c) {\n        if (SPACE_CHARACTERS.has(c)) {\n            this.handleAttributeValue();\n            this.state = State.BeforeAttribute;\n        }\n        else if (c === Chars.SLASH || c === Chars.GT) {\n            this.handleAttributeValue();\n            this.state = State.BeforeTag;\n        }\n        else if (this.attribType === AttribType.Charset) {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    findMetaContentEncoding(c) {\n        if (this.advanceSectionIC(STRINGS.CHARSET, c)) {\n            if (this.sectionIndex === STRINGS.CHARSET.length) {\n                return true;\n            }\n        }\n        else {\n            // If we encountered another `c`, assume we started over.\n            this.sectionIndex = Number(c === STRINGS.CHARSET[0]);\n        }\n        return false;\n    }\n    stateMetaContentValueUnquotedBeforeEncoding(c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            this.stateAttributeValueUnquoted(c);\n        }\n        else if (this.sectionIndex === STRINGS.CHARSET.length) {\n            if (c === Chars.EQUALS) {\n                this.state = State.MetaContentValueUnquotedBeforeValue;\n            }\n        }\n        else {\n            this.findMetaContentEncoding(c);\n        }\n    }\n    stateMetaContentValueUnquotedBeforeValue(c) {\n        if (isQuote(c)) {\n            this.quoteCharacter = c;\n            this.state = State.MetaContentValueUnquotedValueQuoted;\n        }\n        else if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            // Can't have spaces here, as it would no longer be part of the attribute value.\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.state = State.MetaContentValueUnquotedValueUnquoted;\n            this.stateMetaContentValueUnquotedValueUnquoted(c);\n        }\n    }\n    stateMetaContentValueUnquotedValueQuoted(c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c)) {\n            // Quotes weren't matched, so we're done.\n            this.stateAttributeValueUnquoted(c);\n        }\n        else if (c === this.quoteCharacter) {\n            this.handleMetaContentValue();\n            this.state = State.AttributeValueUnquoted;\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueUnquotedValueUnquoted(c) {\n        if (END_OF_UNQUOTED_ATTRIBUTE_VALUE.has(c) || c === Chars.SEMICOLON) {\n            this.handleMetaContentValue();\n            this.state = State.AttributeValueUnquoted;\n            this.stateAttributeValueUnquoted(c);\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueQuotedValueUnquoted(c) {\n        if (isQuote(c) || SPACE_CHARACTERS.has(c) || c === Chars.SEMICOLON) {\n            this.handleMetaContentValue();\n            // We are done with the value, but might not be at the end of the attribute\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueQuotedValueQuoted(c) {\n        if (isQuote(c)) {\n            // We have reached the end of our value.\n            if (c !== this.quoteCharacter) {\n                // Only handle the value if inner quotes were matched.\n                this.handleMetaContentValue();\n            }\n            this.state = State.AttributeValueQuoted;\n            this.stateAttributeValueQuoted(c);\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    stateMetaContentValueQuotedBeforeEncoding(c) {\n        if (c === this.quoteCharacter) {\n            this.stateAttributeValueQuoted(c);\n        }\n        else if (this.findMetaContentEncoding(c)) {\n            this.state = State.MetaContentValueQuotedAfterEncoding;\n        }\n    }\n    stateMetaContentValueQuotedAfterEncoding(c) {\n        if (c === Chars.EQUALS) {\n            this.state = State.MetaContentValueQuotedBeforeValue;\n        }\n        else if (!SPACE_CHARACTERS.has(c)) {\n            // Look for the next encoding\n            this.state = State.MetaContentValueQuotedBeforeEncoding;\n            this.stateMetaContentValueQuotedBeforeEncoding(c);\n        }\n    }\n    stateMetaContentValueQuotedBeforeValue(c) {\n        if (c === this.quoteCharacter) {\n            this.stateAttributeValueQuoted(c);\n        }\n        else if (isQuote(c)) {\n            this.state = State.MetaContentValueQuotedValueQuoted;\n        }\n        else if (!SPACE_CHARACTERS.has(c)) {\n            this.state = State.MetaContentValueQuotedValueUnquoted;\n            this.stateMetaContentValueQuotedValueUnquoted(c);\n        }\n    }\n    stateAttributeValueQuoted(c) {\n        if (c === this.quoteCharacter) {\n            this.handleAttributeValue();\n            this.state = State.BeforeAttribute;\n        }\n        else if (this.attribType === AttribType.Charset) {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    // Read STRINGS.XML_DECLARATION\n    stateXMLDeclaration(c) {\n        if (this.advanceSection(STRINGS.XML_DECLARATION, c)) {\n            if (this.sectionIndex === STRINGS.XML_DECLARATION.length) {\n                this.sectionIndex = 0;\n                this.state = State.XMLDeclarationBeforeEncoding;\n            }\n        }\n        else {\n            this.state = State.WeirdTag;\n        }\n    }\n    stateXMLDeclarationBeforeEncoding(c) {\n        if (this.advanceSection(STRINGS.ENCODING, c)) {\n            if (this.sectionIndex === STRINGS.ENCODING.length) {\n                this.state = State.XMLDeclarationAfterEncoding;\n            }\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else {\n            // If we encountered another `c`, assume we started over.\n            this.sectionIndex = Number(c === STRINGS.ENCODING[0]);\n        }\n    }\n    stateXMLDeclarationAfterEncoding(c) {\n        if (c === Chars.EQUALS) {\n            this.state = State.XMLDeclarationBeforeValue;\n        }\n        else if (c > Chars.SPACE) {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    }\n    stateXMLDeclarationBeforeValue(c) {\n        if (isQuote(c)) {\n            this.attributeValue.length = 0;\n            this.state = State.XMLDeclarationValue;\n        }\n        else if (c > Chars.SPACE) {\n            this.state = State.WeirdTag;\n            this.stateWeirdTag(c);\n        }\n    }\n    stateXMLDeclarationValue(c) {\n        if (isQuote(c)) {\n            this.setResult(String.fromCharCode(...this.attributeValue), ResultType.XML_ENCODING);\n            this.state = State.WeirdTag;\n        }\n        else if (c === Chars.GT) {\n            this.state = State.BeforeTag;\n        }\n        else if (c <= Chars.SPACE) {\n            this.state = State.WeirdTag;\n        }\n        else {\n            this.attributeValue.push(c | (c >= 0x41 && c <= 0x5a ? 0x20 : 0));\n        }\n    }\n    write(buffer) {\n        let index = 0;\n        for (; index < buffer.length && this.offset + index < this.maxBytes; index++) {\n            const c = buffer[index];\n            switch (this.state) {\n                case State.Begin: {\n                    this.stateBegin(c);\n                    break;\n                }\n                case State.BOM16BE: {\n                    this.stateBOM16BE(c);\n                    break;\n                }\n                case State.BOM16LE: {\n                    this.stateBOM16LE(c);\n                    break;\n                }\n                case State.BOM8: {\n                    this.stateBOM8(c);\n                    break;\n                }\n                case State.UTF16LE_XML_PREFIX: {\n                    this.stateUTF16LE_XML_PREFIX(c);\n                    break;\n                }\n                case State.BeginLT: {\n                    this.stateBeginLT(c);\n                    break;\n                }\n                case State.UTF16BE_XML_PREFIX: {\n                    this.stateUTF16BE_XML_PREFIX(c);\n                    break;\n                }\n                case State.BeforeTag: {\n                    // Optimization: Skip all characters until we find a `<`\n                    const idx = buffer.indexOf(Chars.LT, index);\n                    if (idx === -1) {\n                        // We are done with this buffer. Stay in the state and try on the next one.\n                        index = buffer.length;\n                    }\n                    else {\n                        index = idx;\n                        this.stateBeforeTag(Chars.LT);\n                    }\n                    break;\n                }\n                case State.BeforeTagName: {\n                    this.stateBeforeTagName(c);\n                    break;\n                }\n                case State.BeforeCloseTagName: {\n                    this.stateBeforeCloseTagName(c);\n                    break;\n                }\n                case State.CommentStart: {\n                    this.stateCommentStart(c);\n                    break;\n                }\n                case State.CommentEnd: {\n                    this.stateCommentEnd(c);\n                    break;\n                }\n                case State.TagNameMeta: {\n                    this.stateTagNameMeta(c);\n                    break;\n                }\n                case State.TagNameOther: {\n                    this.stateTagNameOther(c);\n                    break;\n                }\n                case State.XMLDeclaration: {\n                    this.stateXMLDeclaration(c);\n                    break;\n                }\n                case State.XMLDeclarationBeforeEncoding: {\n                    this.stateXMLDeclarationBeforeEncoding(c);\n                    break;\n                }\n                case State.XMLDeclarationAfterEncoding: {\n                    this.stateXMLDeclarationAfterEncoding(c);\n                    break;\n                }\n                case State.XMLDeclarationBeforeValue: {\n                    this.stateXMLDeclarationBeforeValue(c);\n                    break;\n                }\n                case State.XMLDeclarationValue: {\n                    this.stateXMLDeclarationValue(c);\n                    break;\n                }\n                case State.WeirdTag: {\n                    this.stateWeirdTag(c);\n                    break;\n                }\n                case State.BeforeAttribute: {\n                    this.stateBeforeAttribute(c);\n                    break;\n                }\n                case State.MetaAttribHttpEquiv: {\n                    this.stateMetaAttribHttpEquiv(c);\n                    break;\n                }\n                case State.MetaAttribHttpEquivValue: {\n                    this.stateMetaAttribHttpEquivValue(c);\n                    break;\n                }\n                case State.MetaAttribC: {\n                    this.stateMetaAttribC(c);\n                    break;\n                }\n                case State.MetaAttribContent: {\n                    this.stateMetaAttribContent(c);\n                    break;\n                }\n                case State.MetaAttribCharset: {\n                    this.stateMetaAttribCharset(c);\n                    break;\n                }\n                case State.MetaAttribAfterName: {\n                    this.stateMetaAttribAfterName(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedBeforeEncoding: {\n                    this.stateMetaContentValueQuotedBeforeEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedAfterEncoding: {\n                    this.stateMetaContentValueQuotedAfterEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedBeforeValue: {\n                    this.stateMetaContentValueQuotedBeforeValue(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedValueQuoted: {\n                    this.stateMetaContentValueQuotedValueQuoted(c);\n                    break;\n                }\n                case State.MetaContentValueQuotedValueUnquoted: {\n                    this.stateMetaContentValueQuotedValueUnquoted(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedBeforeEncoding: {\n                    this.stateMetaContentValueUnquotedBeforeEncoding(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedBeforeValue: {\n                    this.stateMetaContentValueUnquotedBeforeValue(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedValueQuoted: {\n                    this.stateMetaContentValueUnquotedValueQuoted(c);\n                    break;\n                }\n                case State.MetaContentValueUnquotedValueUnquoted: {\n                    this.stateMetaContentValueUnquotedValueUnquoted(c);\n                    break;\n                }\n                case State.AnyAttribName: {\n                    this.stateAnyAttribName(c);\n                    break;\n                }\n                case State.AfterAttributeName: {\n                    this.stateAfterAttributeName(c);\n                    break;\n                }\n                case State.BeforeAttributeValue: {\n                    this.stateBeforeAttributeValue(c);\n                    break;\n                }\n                case State.AttributeValueQuoted: {\n                    this.stateAttributeValueQuoted(c);\n                    break;\n                }\n                case State.AttributeValueUnquoted: {\n                    this.stateAttributeValueUnquoted(c);\n                    break;\n                }\n            }\n        }\n        this.offset += index;\n    }\n}\n/** Get the encoding for the passed buffer. */\nfunction getEncoding(buffer, options) {\n    const sniffer = new Sniffer(options);\n    sniffer.write(buffer);\n    return sniffer.encoding;\n}\n//# sourceMappingURL=sniffer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/encoding-sniffer/dist/esm/sniffer.js\n");

/***/ })

};
;