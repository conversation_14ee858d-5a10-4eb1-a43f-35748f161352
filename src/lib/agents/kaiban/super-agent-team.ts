/**
 * KaibanJS Super Agent Team
 *
 * This file creates and configures the main KaibanJS team that orchestrates
 * the entire super agent workflow with all agents and tasks.
 */

import { Team } from 'kaibanjs';
import { superAgentTeam } from './agents';
import { superAgentTasks } from './tasks';

// Console logging utility for Kaiban Super Agent Team
const logKaibanTeam = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`🚀 [KAIBAN-TEAM] ${timestamp}: ${message}`);
  if (data) {
    console.log(`📈 [KAIBAN-TEAM-DATA]:`, data);
  }
};

// Console logging utility for workflow progress
const logKaibanProgress = (phase: string, progress: number, message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`⚡ [KAIBAN-PROGRESS] ${timestamp}: [${phase}] ${progress}% - ${message}`);
  if (data) {
    console.log(`📊 [KAIBAN-PROGRESS-DATA]:`, data);
  }
};

/**
 * Super Agent Team Configuration
 * Defines the complete workflow with all agents and tasks
 */
export interface SuperAgentOptions {
  topic: string;
  contentType?: 'article' | 'blog-post' | 'research-paper' | 'comprehensive-guide';
  targetWordCount?: number;
  tone?: 'professional' | 'casual' | 'academic' | 'conversational';
  targetAudience?: 'beginner' | 'intermediate' | 'expert' | 'general';
  maxPrimaryResults?: number;
  maxDeepResults?: number;
  enableFactChecking?: boolean;
  includeSourceCitations?: boolean;
}

/**
 * Super Agent Result Interface
 * Defines the structure of the final result from the workflow
 */
export interface SuperAgentResult {
  success: boolean;
  topic: string;
  executionTime: number;

  // Phase Results
  topicAnalysis?: any;
  contentStrategy?: any;
  primaryResearch?: any;
  gapAnalysis?: any;
  deepResearch?: any;
  generatedContent?: any;
  qualityAssurance?: any;

  // Final Output
  title?: string;
  content?: string;
  wordCount?: number;
  qualityScore?: number;
  sourcesUsed?: number;

  // Metadata
  agentPerformance?: any;
  workflowLogs?: any[];
  error?: string;
}

/**
 * Create Super Agent Team
 * Factory function to create a configured KaibanJS team
 */
export function createSuperAgentTeam(
  options: SuperAgentOptions,
  onProgress?: (phase: string, progress: number, message: string) => void
): Team {

  logKaibanTeam('Creating Super Agent Team', {
    topic: options.topic,
    contentType: options.contentType,
    targetWordCount: options.targetWordCount,
    tone: options.tone,
    targetAudience: options.targetAudience
  });

  // Set default options
  const defaultOptions: Required<SuperAgentOptions> = {
    topic: options.topic,
    contentType: options.contentType || 'article',
    targetWordCount: options.targetWordCount || 2000,
    tone: options.tone || 'professional',
    targetAudience: options.targetAudience || 'intermediate',
    maxPrimaryResults: options.maxPrimaryResults || 6,
    maxDeepResults: options.maxDeepResults || 4,
    enableFactChecking: options.enableFactChecking ?? true,
    includeSourceCitations: options.includeSourceCitations ?? true
  };

  logKaibanTeam('Default options configured', defaultOptions);

  // Create the team with all agents and tasks
  logKaibanTeam('Initializing KaibanJS Team', {
    teamName: 'Super Agent Content Creation Team',
    totalAgents: 7,
    totalTasks: superAgentTasks.length,
    memoryEnabled: true
  });

  const team = new Team({
    name: 'Super Agent Content Creation Team',
    agents: [
      superAgentTeam.topicAnalysisAgent,
      superAgentTeam.contentStrategyAgent,
      superAgentTeam.primaryResearchAgent,
      superAgentTeam.gapAnalysisAgent,
      superAgentTeam.deepResearchAgent,
      superAgentTeam.contentGenerationAgent,
      superAgentTeam.qualityAssuranceAgent
    ],
    tasks: superAgentTasks,
    inputs: {
      topic: defaultOptions.topic,
      contentType: defaultOptions.contentType,
      targetWordCount: defaultOptions.targetWordCount,
      tone: defaultOptions.tone,
      targetAudience: defaultOptions.targetAudience,
      maxPrimaryResults: defaultOptions.maxPrimaryResults,
      maxDeepResults: defaultOptions.maxDeepResults,
      enableFactChecking: defaultOptions.enableFactChecking,
      includeSourceCitations: defaultOptions.includeSourceCitations
    },
    env: {
      OPENAI_API_KEY: process.env.OPENAI_API_KEY || '',
      OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || '',
      GOOGLE_SEARCH_API_KEY: process.env.GOOGLE_SEARCH_API_KEY || '',
      GOOGLE_SEARCH_ENGINE_ID: process.env.GOOGLE_SEARCH_ENGINE_ID || '',
      GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
      // OpenRouter configuration for high reasoning phases
      OPENROUTER_BASE_URL: 'https://openrouter.ai/api/v1'
    },
    memory: true // Enable memory for task result passing
  });

  logKaibanTeam('KaibanJS Team created successfully', {
    teamConfigured: true,
    inputsSet: true,
    environmentConfigured: true
  });

  // Set up progress tracking if callback provided
  if (onProgress) {
    logKaibanTeam('Setting up progress tracking', {
      progressCallbackProvided: true,
      trackingEnabled: true
    });

    const useStore = team.useStore();

    // Subscribe to workflow logs for progress tracking
    useStore.subscribe(
      (state: any) => state.workflowLogs,
      (newLogs: any[]) => {
        const state = useStore.getState();
        const previousLogs = state.workflowLogs || [];

        if (newLogs.length > previousLogs.length) {
          const latestLog = newLogs[newLogs.length - 1];

          logKaibanProgress('workflow-update', 0, 'New workflow log received', {
            logType: latestLog.logType,
            totalLogs: newLogs.length,
            previousLogs: previousLogs.length
          });

          if (latestLog.logType === 'TaskStatusUpdate') {
            const { task, agent } = latestLog;
            const progress = calculateProgress(task, newLogs);
            const message = `${agent.name}: ${getTaskStatusMessage(task.status)}`;
            const phase = getPhaseFromTask(task);

            logKaibanProgress(phase, progress, message, {
              taskName: task.description,
              agentName: agent.name,
              taskStatus: task.status,
              calculatedProgress: progress
            });

            onProgress(phase, progress, message);
          }
        }
      }
    );
  } else {
    logKaibanTeam('No progress callback provided - running without progress tracking');
  }

  logKaibanTeam('Team setup complete - ready for execution');
  return team;
}

/**
 * Execute Super Agent Workflow
 * Main function to execute the complete workflow
 */
export async function executeSuperAgentWorkflow(
  options: SuperAgentOptions,
  onProgress?: (phase: string, progress: number, message: string) => void
): Promise<SuperAgentResult> {

  const startTime = Date.now();

  logKaibanTeam('🚀 Starting Super Agent Workflow Execution', {
    topic: options.topic,
    contentType: options.contentType || 'article',
    targetWordCount: options.targetWordCount || 2000,
    tone: options.tone || 'professional',
    startTime: new Date(startTime).toISOString(),
    progressTrackingEnabled: !!onProgress
  });

  try {
    // Create and start the team
    logKaibanTeam('Creating team for workflow execution');
    const team = createSuperAgentTeam(options, onProgress);

    logKaibanTeam('🎯 Starting team execution - workflow beginning');
    const result = await team.start();

    const executionTime = Date.now() - startTime;

    logKaibanTeam('✅ Team execution completed', {
      executionTimeMs: executionTime,
      executionTimeSeconds: Math.round(executionTime / 1000),
      success: true
    });

    // Extract results from the workflow
    logKaibanTeam('Extracting workflow results');
    const workflowResult = extractWorkflowResults(result, team);

    const finalResult = {
      success: true,
      topic: options.topic,
      executionTime,
      ...workflowResult
    };

    logKaibanTeam('🎉 Super Agent Workflow completed successfully', {
      topic: options.topic,
      executionTime,
      title: finalResult.title,
      wordCount: finalResult.wordCount,
      qualityScore: finalResult.qualityScore,
      sourcesUsed: finalResult.sourcesUsed
    });

    return finalResult;

  } catch (error) {
    const executionTime = Date.now() - startTime;

    logKaibanTeam('❌ Super Agent Workflow failed', {
      topic: options.topic,
      executionTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined
    });

    return {
      success: false,
      topic: options.topic,
      executionTime,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Helper Functions
 */

function calculateProgress(task: any, logs: any[]): number {
  // Calculate progress based on task completion and current phase
  const totalTasks = 7;
  const completedTasks = logs.filter(log =>
    log.logType === 'TaskStatusUpdate' && log.task.status === 'DONE'
  ).length;

  const progress = Math.round((completedTasks / totalTasks) * 100);

  logKaibanTeam('Progress calculated', {
    totalTasks,
    completedTasks,
    progress,
    currentTaskDescription: task.description,
    currentTaskStatus: task.status
  });

  return progress;
}

function getTaskStatusMessage(status: string): string {
  const statusMessages: { [key: string]: string } = {
    'TODO': 'Preparing to start',
    'DOING': 'Working on task',
    'DONE': 'Task completed',
    'BLOCKED': 'Task blocked',
    'REVISE': 'Revising work'
  };

  const message = statusMessages[status] || 'Processing';

  logKaibanTeam('Task status message generated', {
    status,
    message
  });

  return message;
}

function getPhaseFromTask(task: any): string {
  const taskPhases: { [key: string]: string } = {
    'Topic Analysis': 'topic-analysis',
    'Content Strategy': 'content-strategy',
    'Primary Research': 'primary-research',
    'Gap Analysis': 'gap-analysis',
    'Deep Research': 'deep-research',
    'Content Generation': 'content-generation',
    'Quality Assurance': 'quality-assurance'
  };

  const phase = taskPhases[task.description] || 'processing';

  logKaibanTeam('Phase determined from task', {
    taskDescription: task.description,
    phase
  });

  return phase;
}

function extractWorkflowResults(result: any, team: Team): Partial<SuperAgentResult> {
  logKaibanTeam('🔍 Starting workflow result extraction');

  try {
    const useStore = team.useStore();
    const state = useStore.getState();

    logKaibanTeam('Team state retrieved', {
      totalTasks: state.tasks?.length || 0,
      totalAgents: state.agents?.length || 0,
      totalLogs: state.workflowLogs?.length || 0
    });

    // Extract task results
    const taskResults = state.tasks.map((task: any) => ({
      name: task.description,
      status: task.status,
      result: task.result
    }));

    logKaibanTeam('Task results extracted', {
      totalTasks: taskResults.length,
      taskStatuses: taskResults.map(t => ({ name: t.name, status: t.status }))
    });

    // Extract final content from the last task (Quality Assurance)
    const finalTask = taskResults[taskResults.length - 1];
    const contentTask = taskResults[taskResults.length - 2]; // Content Generation task

    logKaibanTeam('Identifying final tasks', {
      finalTaskName: finalTask?.name,
      finalTaskStatus: finalTask?.status,
      contentTaskName: contentTask?.name,
      contentTaskStatus: contentTask?.status
    });

    let finalContent: any = {};
    let qualityMetrics: any = {};

    try {
      if (contentTask?.result) {
        finalContent = typeof contentTask.result === 'string'
          ? JSON.parse(contentTask.result)
          : contentTask.result;

        logKaibanTeam('Content task result parsed', {
          hasTitle: !!finalContent.title,
          hasContent: !!finalContent.content,
          wordCount: finalContent.wordCount || 0
        });
      }

      if (finalTask?.result) {
        qualityMetrics = typeof finalTask.result === 'string'
          ? JSON.parse(finalTask.result)
          : finalTask.result;

        logKaibanTeam('Quality metrics parsed', {
          hasQualityScore: !!qualityMetrics.overallQualityScore,
          qualityScore: qualityMetrics.overallQualityScore || 0
        });
      }
    } catch (parseError) {
      logKaibanTeam('⚠️ Error parsing task results', {
        error: parseError instanceof Error ? parseError.message : 'Unknown parse error',
        contentTaskResult: contentTask?.result,
        finalTaskResult: finalTask?.result
      });
    }

    const extractedResults = {
      topicAnalysis: taskResults[0]?.result,
      contentStrategy: taskResults[1]?.result,
      primaryResearch: taskResults[2]?.result,
      gapAnalysis: taskResults[3]?.result,
      deepResearch: taskResults[4]?.result,
      generatedContent: finalContent,
      qualityAssurance: qualityMetrics,

      title: finalContent.title || 'Generated Content',
      content: finalContent.content || '',
      wordCount: finalContent.wordCount || 0,
      qualityScore: qualityMetrics.overallQualityScore || 0,
      sourcesUsed: finalContent.sourcesUsed?.length || 0,

      agentPerformance: state.agents.map((agent: any) => ({
        name: agent.name,
        status: agent.status,
        tasksCompleted: agent.tasksCompleted || 0
      })),
      workflowLogs: state.workflowLogs || []
    };

    logKaibanTeam('✅ Workflow results extracted successfully', {
      title: extractedResults.title,
      contentLength: extractedResults.content?.length || 0,
      wordCount: extractedResults.wordCount,
      qualityScore: extractedResults.qualityScore,
      sourcesUsed: extractedResults.sourcesUsed,
      agentCount: extractedResults.agentPerformance?.length || 0,
      logCount: extractedResults.workflowLogs?.length || 0,
      allTasksCompleted: taskResults.every(t => t.status === 'DONE')
    });

    return extractedResults;

  } catch (error) {
    logKaibanTeam('❌ Error extracting workflow results', {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined
    });

    return {
      title: 'Content Generation Error',
      content: 'An error occurred during content generation.',
      wordCount: 0,
      qualityScore: 0,
      sourcesUsed: 0
    };
  }
}

/**
 * Export the main team creation function as default
 */
export { createSuperAgentTeam as default };
