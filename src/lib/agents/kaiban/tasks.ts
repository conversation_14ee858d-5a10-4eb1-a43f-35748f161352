/**
 * KaibanJS Tasks for Super Agent Workflow
 *
 * This file defines all the tasks that agents will execute in sequence
 * to complete the super agent workflow using KaibanJS framework.
 */

import { Task } from 'kaibanjs';
import { superAgentTeam } from './agents';

// Console logging utility for Kaiban Tasks
const logKaibanTask = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`📋 [KAIBAN-TASK] ${timestamp}: ${message}`);
  if (data) {
    console.log(`📝 [KAIBAN-TASK-DATA]:`, data);
  }
};

// Log task system initialization
logKaibanTask('Initializing Kaiban Task System', {
  totalTasks: 7,
  taskSequence: [
    'Topic Analysis',
    'Content Strategy',
    'Primary Research',
    'Gap Analysis',
    'Deep Research',
    'Content Generation',
    'Quality Assurance'
  ]
});

/**
 * Task 1: Topic Analysis
 * Analyze the input topic and generate research strategy
 */
logKaibanTask('Creating Topic Analysis Task', {
  taskNumber: 1,
  taskName: 'Topic Analysis',
  agent: 'Topic Analyzer',
  expectedOutputs: ['mainTopic', 'keySubtopics', 'primaryKeywords', 'researchQueries']
});

export const topicAnalysisTask = new Task({
  description: `As a Topic Analysis Specialist, analyze the topic "{topic}" using advanced reasoning and systematic methodology.

**REASONING APPROACH**: Use chain-of-thought reasoning to break down this complex analysis task:

**Step 1: Topic Deconstruction**
- Identify the core topic and its fundamental components
- Map out primary and secondary subtopics
- Determine topic scope and boundaries
- Assess topic complexity and depth requirements

**Step 2: Keyword Strategy Development**
- Generate 8-12 primary keywords with high search intent
- Identify 15-20 semantic keywords and LSI terms
- Create keyword clusters for content organization
- Analyze keyword difficulty and search volume potential

**Step 3: Research Query Formulation**
- Develop 6-10 targeted search queries using advanced search operators
- Create queries for different research phases (broad, specific, expert-level)
- Include queries for recent developments and trends (2024-2025)
- Design queries for finding authoritative sources and case studies

**Step 4: Content Angle Analysis**
- Identify 4-6 unique content perspectives
- Analyze competitor content gaps and opportunities
- Determine trending angles and emerging viewpoints
- Assess content differentiation potential

**Step 5: Audience Profiling**
- Define primary audience demographics, psychographics, and pain points
- Identify secondary audience segments and their specific needs
- Determine audience knowledge level and content consumption preferences
- Map audience journey and content touchpoints

**Step 6: Competitive Intelligence**
- Analyze existing content landscape and saturation level
- Identify content gaps and underserved areas
- Assess competitor strengths and weaknesses
- Determine content differentiation opportunities

**CRITICAL SUCCESS FACTORS**:
- Ensure all analysis is data-driven and evidence-based
- Focus on actionable insights for subsequent research phases
- Maintain consistency with user's specified content type and target audience
- Prioritize 2025 trends and forward-looking perspectives

**OUTPUT REQUIREMENTS**: Structure your response as valid JSON following this exact schema:`,

  expectedOutput: `A comprehensive topic analysis in JSON format with this exact structure:
{
  "mainTopic": "string - core topic clearly defined",
  "topicScope": "string - boundaries and focus areas",
  "keySubtopics": ["array of 5-8 primary subtopics"],
  "primaryKeywords": ["array of 8-12 high-intent keywords"],
  "semanticKeywords": ["array of 15-20 LSI and semantic terms"],
  "keywordClusters": {
    "cluster1": ["related keywords"],
    "cluster2": ["related keywords"]
  },
  "researchQueries": [
    {
      "query": "search query string",
      "purpose": "broad/specific/expert/trends",
      "expectedSources": "type of sources to find"
    }
  ],
  "contentAngles": [
    {
      "angle": "perspective name",
      "description": "detailed explanation",
      "uniqueness": "differentiation factor",
      "targetSegment": "audience segment"
    }
  ],
  "targetAudience": {
    "primary": {
      "demographics": "age, profession, location",
      "psychographics": "interests, values, motivations",
      "painPoints": ["specific challenges"],
      "knowledgeLevel": "beginner/intermediate/advanced",
      "contentPreferences": "format and style preferences"
    },
    "secondary": {
      "demographics": "secondary audience details",
      "specificNeeds": "unique requirements"
    }
  },
  "contentComplexity": "beginner/intermediate/advanced with justification",
  "competitiveAnalysis": {
    "contentSaturation": "high/medium/low",
    "identifiedGaps": ["specific content gaps"],
    "competitorStrengths": ["what competitors do well"],
    "differentiationOpportunities": ["how to stand out"]
  },
  "trendAnalysis": {
    "currentTrends": ["2024-2025 relevant trends"],
    "emergingTopics": ["upcoming areas of interest"],
    "seasonalFactors": "timing considerations"
  },
  "recommendedApproach": "strategic recommendation for content creation",
  "successMetrics": ["how to measure content success"],
  "confidenceScore": "1-10 rating of analysis confidence"
}`,

  agent: superAgentTeam.topicAnalysisAgent
});

logKaibanTask('Topic Analysis Task created successfully');

/**
 * Task 2: Content Strategy Development
 * Develop comprehensive content strategy based on topic analysis
 */
logKaibanTask('Creating Content Strategy Task', {
  taskNumber: 2,
  taskName: 'Content Strategy',
  agent: 'Content Strategist',
  dependsOn: 'Topic Analysis Task',
  expectedOutputs: ['contentStructure', 'keyMessages', 'seoStrategy']
});

export const contentStrategyTask = new Task({
  description: `As a Content Strategy Director, develop a comprehensive content strategy using the topic analysis: {taskResult:task1}

**STRATEGIC FRAMEWORK**: Apply systematic content strategy methodology:

**Step 1: Strategy Foundation Analysis**
- Analyze topic analysis insights and competitive landscape
- Align strategy with content specifications:
  * Content Type: {contentType}
  * Target Word Count: {targetWordCount}
  * Tone: {tone}
  * Target Audience: {targetAudience}
- Identify primary content objectives and success metrics

**Step 2: Content Architecture Design**
- Create hierarchical content structure with logical flow
- Design section-by-section outline with word count allocation
- Plan content depth and detail level for each section
- Ensure structure supports target word count and reading experience

**Step 3: Message Strategy Development**
- Craft 4-6 core messages that resonate with target audience
- Develop supporting arguments and evidence requirements
- Create message hierarchy and emphasis points
- Align messages with audience pain points and interests

**Step 4: Engagement Strategy Planning**
- Design attention-grabbing hooks and opening strategies
- Plan storytelling elements, examples, and case studies
- Identify opportunities for interactive elements and visuals
- Create reader retention and engagement touchpoints

**Step 5: SEO Integration Strategy**
- Develop keyword integration plan using topic analysis keywords
- Plan natural keyword placement and density
- Design meta elements and SEO-friendly structure
- Create internal linking and content optimization strategy

**Step 6: Differentiation and Positioning**
- Leverage competitive analysis to identify unique positioning
- Plan content elements that differentiate from competitors
- Design value propositions and unique insights
- Create memorable and shareable content elements

**QUALITY STANDARDS**:
- Ensure strategy aligns with 2025 content trends and best practices
- Focus on reader value and actionable insights
- Maintain consistency with specified tone and audience preferences
- Plan for measurable engagement and conversion outcomes

**OUTPUT REQUIREMENTS**: Provide detailed strategy in valid JSON format:`,

  expectedOutput: `A comprehensive content strategy in JSON format with this exact structure:
{
  "strategyOverview": {
    "primaryObjective": "main content goal",
    "secondaryObjectives": ["supporting goals"],
    "targetMetrics": ["measurable success indicators"],
    "contentPillars": ["3-4 main content themes"]
  },
  "contentStructure": {
    "introduction": {
      "wordCount": "number",
      "keyElements": ["hook", "preview", "value proposition"],
      "estimatedReadTime": "minutes"
    },
    "mainSections": [
      {
        "sectionTitle": "section name",
        "wordCount": "allocated words",
        "keyPoints": ["main points to cover"],
        "supportingElements": ["examples", "data", "quotes"],
        "transitionStrategy": "how to connect to next section"
      }
    ],
    "conclusion": {
      "wordCount": "number",
      "keyElements": ["summary", "call-to-action", "next steps"],
      "memorableClosing": "final impression strategy"
    }
  },
  "keyMessages": [
    {
      "message": "core message text",
      "priority": "high/medium/low",
      "placement": "where in content",
      "supportingEvidence": "type of proof needed",
      "audienceResonance": "why it matters to readers"
    }
  ],
  "contentFlow": {
    "narrativeArc": "overall story progression",
    "logicalProgression": ["step-by-step flow"],
    "transitionStrategies": ["how sections connect"],
    "paceAndRhythm": "content pacing strategy"
  },
  "engagementElements": {
    "openingHooks": ["attention-grabbing techniques"],
    "storytellingElements": ["narratives", "case studies", "examples"],
    "interactiveElements": ["questions", "exercises", "tools"],
    "visualElements": ["charts", "infographics", "images"],
    "retentionTactics": ["techniques to keep readers engaged"]
  },
  "seoStrategy": {
    "primaryKeywords": ["main SEO targets with placement"],
    "secondaryKeywords": ["supporting terms with usage"],
    "keywordDensity": "target percentage",
    "headingStrategy": "H1, H2, H3 keyword integration",
    "metaElements": {
      "title": "SEO-optimized title",
      "description": "meta description",
      "focusKeyphrase": "primary SEO target"
    },
    "internalLinking": "linking strategy"
  },
  "differentiationStrategy": {
    "uniqueAngle": "what makes this content different",
    "competitiveAdvantages": ["how we beat competitors"],
    "valueProposition": "unique value for readers",
    "memorabilityFactors": ["what makes it stick"]
  },
  "callToAction": {
    "primary": "main action for readers",
    "secondary": ["alternative actions"],
    "placement": "where CTAs appear",
    "persuasionTechniques": ["psychological triggers"]
  },
  "qualityAssurance": {
    "readabilityTarget": "grade level/score",
    "toneConsistency": "tone maintenance strategy",
    "factCheckingNeeds": ["areas requiring verification"],
    "reviewCriteria": ["quality checkpoints"]
  },
  "estimatedMetrics": {
    "readingTime": "minutes",
    "engagementScore": "predicted 1-10",
    "shareabilityFactor": "viral potential 1-10",
    "conversionPotential": "action likelihood 1-10"
  }
}`,

  agent: superAgentTeam.contentStrategyAgent
});

logKaibanTask('Content Strategy Task created successfully');

/**
 * Task 3: Primary Research
 * Conduct initial research using the research queries from topic analysis
 */
logKaibanTask('Creating Primary Research Task', {
  taskNumber: 3,
  taskName: 'Primary Research',
  agent: 'Primary Researcher',
  dependsOn: 'Topic Analysis Task',
  expectedOutputs: ['researchData', 'keyFindings', 'statisticsAndData']
});

export const primaryResearchTask = new Task({
  description: `As a Primary Research Specialist, conduct systematic research for "{topic}" using the strategic framework from: {taskResult:task1}

**RESEARCH METHODOLOGY**: Execute comprehensive research using proven academic and industry standards:

**Phase 1: Research Planning and Query Optimization**
- Analyze research queries from topic analysis for maximum effectiveness
- Optimize search queries using advanced Google search operators
- Plan research sequence from broad to specific information gathering
- Establish source quality criteria and evaluation framework

**Phase 2: Systematic Google Search Execution**
- Use the google_search tool to execute each research query
- Apply advanced search operators for precision: site:, filetype:, intitle:, inurl:
- Search for recent content (2024-2025) using date filters
- Target authoritative domains: .edu, .gov, industry leaders, expert publications
- Maximum results per query: {maxPrimaryResults}
- Use the tool by calling: google_search with your search query as input
- Example tool usage: google_search("artificial intelligence trends 2025")
- Process the JSON response from the tool to extract search results and content

**Phase 3: Source Evaluation and Credibility Assessment**
- Evaluate each source using academic credibility criteria:
  * Author expertise and credentials
  * Publication reputation and authority
  * Content accuracy and fact-checking
  * Recency and relevance to topic
  * Citation quality and references
- Rate sources on 1-10 reliability scale with detailed justification
- Prioritize peer-reviewed, expert-authored, and authoritative sources

**Phase 4: Content Extraction and Data Mining**
- Extract key information, statistics, and insights from each source
- Identify expert quotes and authoritative statements
- Collect case studies, examples, and real-world applications
- Gather supporting data, charts, and quantitative evidence
- Document methodology and research limitations where applicable

**Phase 5: Data Organization and Synthesis**
- Organize findings by topic relevance and content strategy alignment
- Create thematic clusters of related information
- Identify patterns, trends, and consensus across sources
- Flag conflicting information for further investigation
- Prepare data for seamless integration with content strategy

**RESEARCH QUALITY STANDARDS**:
- Prioritize authoritative and expert sources over general content
- Ensure geographic and demographic diversity in sources when relevant
- Focus on actionable insights and practical applications
- Maintain rigorous fact-checking and source verification
- Document research limitations and potential biases

**CRITICAL SUCCESS FACTORS**:
- Align all research with content strategy requirements from Task 2
- Focus on information that supports key messages and content structure
- Prioritize recent developments and 2025 trends
- Ensure research depth matches target audience knowledge level

**OUTPUT REQUIREMENTS**: Compile comprehensive research in structured JSON format:`,

  expectedOutput: `Primary research data in JSON format. Start with a simple structure and use the google_search tool to gather information:

IMPORTANT: Use the google_search tool to perform actual searches. The tool expects a simple string query and returns JSON with search results and extracted content.

Example tool usage:
1. Call google_search("your search query here")
2. Process the returned JSON response
3. Extract relevant information from the results

Required JSON output structure:
{
  "researchSummary": {
    "totalQueries": 0,
    "totalSources": 0,
    "researchScope": "description of research conducted",
    "keyThemes": ["theme1", "theme2"],
    "researchLimitations": ["limitation1", "limitation2"]
  },
  "researchData": [
    {
      "title": "source title",
      "url": "source URL",
      "keyContent": "main information extracted",
      "reliabilityScore": 8,
      "relevanceScore": 9
    }
  ],
  "synthesizedFindings": {
    "keyInsights": [
      {
        "insight": "major finding",
        "confidence": "high"
      }
    ],
    "statisticalData": [
      {
        "statistic": "key number or data point",
        "source": "where it came from"
      }
    ]
  },
  "researchGaps": {
    "identifiedGaps": ["gap1", "gap2"]
  },
  "sourceQuality": {
    "averageReliability": 8.5,
    "totalSources": 5
  }
}

Remember: Use the google_search tool to perform actual searches and gather real data for this JSON structure.`,

  agent: superAgentTeam.primaryResearchAgent
});

logKaibanTask('Primary Research Task created successfully');

/**
 * Task 4: Gap Analysis
 * Identify gaps in the primary research data
 */
logKaibanTask('Creating Gap Analysis Task', {
  taskNumber: 4,
  taskName: 'Gap Analysis',
  agent: 'Gap Analyst',
  dependsOn: ['Primary Research Task', 'Content Strategy Task'],
  expectedOutputs: ['gapsSummary', 'totalGapsIdentified', 'prioritizedResearchQueries']
});

export const gapAnalysisTask = new Task({
  description: `As a Research Gap Analyst, conduct systematic gap analysis using advanced reasoning to compare:
- Primary Research Data: {taskResult:task3}
- Content Strategy Requirements: {taskResult:task2}

**ANALYTICAL FRAMEWORK**: Apply rigorous gap analysis methodology using chain-of-thought reasoning:

**Step 1: Content Strategy Alignment Assessment**
- Map primary research findings to content strategy requirements
- Identify content structure sections lacking sufficient research support
- Assess alignment between key messages and available evidence
- Evaluate research coverage of target audience needs and pain points

**Step 2: Information Completeness Analysis**
- Analyze information density and depth for each content section
- Identify missing statistics, data points, and quantitative evidence
- Assess coverage of key subtopics and content angles
- Evaluate balance between different types of information (theoretical vs. practical)

**Step 3: Source Authority and Credibility Evaluation**
- Assess authority level of current sources against content requirements
- Identify areas needing more expert perspectives or authoritative sources
- Evaluate geographic, demographic, and industry representation
- Assess need for peer-reviewed or academic sources

**Step 4: Perspective and Viewpoint Analysis**
- Identify underrepresented viewpoints or expert opinions
- Assess balance of perspectives (pro/con, different schools of thought)
- Evaluate coverage of diverse demographics and use cases
- Identify missing stakeholder perspectives

**Step 5: Evidence and Support Material Assessment**
- Evaluate sufficiency of supporting evidence for key claims
- Identify areas lacking case studies, examples, or real-world applications
- Assess need for additional proof points and validation
- Evaluate balance between anecdotal and empirical evidence

**Step 6: Currency and Trend Analysis**
- Assess recency of information against 2025 content standards
- Identify areas needing current trends and developments
- Evaluate coverage of emerging topics and future predictions
- Assess need for recent case studies and contemporary examples

**Step 7: Practical Application and Actionability Gaps**
- Identify missing practical applications and implementation guidance
- Assess coverage of tools, techniques, and methodologies
- Evaluate presence of actionable insights and next steps
- Identify gaps in how-to information and practical examples

**CRITICAL ANALYSIS STANDARDS**:
- Use systematic comparison methodology for objective assessment
- Prioritize gaps based on content strategy impact and audience value
- Focus on actionable gaps that can be addressed through targeted research
- Consider research feasibility and source availability

**OUTPUT REQUIREMENTS**: Provide comprehensive gap analysis in structured JSON format:`,

  expectedOutput: `Comprehensive gap analysis report in JSON format with this exact structure:
{
  "executiveSummary": {
    "totalGapsIdentified": "number",
    "criticalGaps": "number of high-priority gaps",
    "overallCompleteness": "percentage of content strategy covered",
    "researchPriority": "high/medium/low urgency for additional research",
    "keyRecommendations": ["top 3-5 recommendations"]
  },
  "contentAlignmentAnalysis": {
    "strategyCoverage": "percentage of strategy requirements met",
    "wellCoveredAreas": ["areas with sufficient research"],
    "underCoveredAreas": ["areas needing more research"],
    "missingElements": ["completely missing components"]
  },
  "gapCategories": {
    "informationGaps": [
      {
        "gapDescription": "specific information missing",
        "importance": "1-10 scale with justification",
        "impactOnContent": "how this affects content quality",
        "targetedQueries": ["specific search queries to address gap"],
        "recommendedSources": ["types of sources needed"],
        "priority": "high/medium/low",
        "estimatedResearchTime": "time needed to fill gap"
      }
    ],
    "perspectiveGaps": [
      {
        "missingPerspective": "viewpoint or expert opinion needed",
        "importance": "1-10 scale with justification",
        "stakeholderType": "type of expert or perspective needed",
        "targetedQueries": ["queries to find these perspectives"],
        "priority": "high/medium/low"
      }
    ],
    "evidenceGaps": [
      {
        "evidenceType": "type of evidence missing",
        "claimsNeedingSupport": ["specific claims requiring evidence"],
        "importance": "1-10 scale",
        "targetedQueries": ["queries to find supporting evidence"],
        "priority": "high/medium/low"
      }
    ],
    "depthGaps": [
      {
        "topicArea": "area needing deeper exploration",
        "currentDepth": "superficial/moderate/detailed",
        "requiredDepth": "level needed for content strategy",
        "importance": "1-10 scale",
        "targetedQueries": ["queries for deeper research"],
        "priority": "high/medium/low"
      }
    ],
    "currencyGaps": [
      {
        "outdatedArea": "area with outdated information",
        "latestDataDate": "most recent data found",
        "requiredRecency": "how recent information needs to be",
        "importance": "1-10 scale",
        "targetedQueries": ["queries for recent information"],
        "priority": "high/medium/low"
      }
    ],
    "authorityGaps": [
      {
        "topicArea": "area needing more authoritative sources",
        "currentAuthorityLevel": "assessment of current sources",
        "requiredAuthorityLevel": "level needed",
        "importance": "1-10 scale",
        "targetedQueries": ["queries for authoritative sources"],
        "priority": "high/medium/low"
      }
    ],
    "practicalGaps": [
      {
        "practicalArea": "area missing practical application",
        "theoreticalCoverage": "level of theoretical information",
        "practicalNeed": "type of practical information needed",
        "importance": "1-10 scale",
        "targetedQueries": ["queries for practical examples"],
        "priority": "high/medium/low"
      }
    ]
  },
  "prioritizedResearchPlan": {
    "highPriorityGaps": [
      {
        "gap": "gap description",
        "rationale": "why this is high priority",
        "researchQueries": ["specific queries"],
        "expectedOutcome": "what this research will achieve",
        "timeEstimate": "estimated research time"
      }
    ],
    "mediumPriorityGaps": ["similar structure for medium priority"],
    "lowPriorityGaps": ["similar structure for low priority"]
  },
  "researchRecommendations": {
    "immediateActions": ["actions to take first"],
    "researchSequence": ["recommended order of research"],
    "sourceTargets": ["specific types of sources to prioritize"],
    "searchStrategies": ["recommended search approaches"],
    "qualityThresholds": ["minimum quality standards for new sources"]
  },
  "riskAssessment": {
    "contentQualityRisks": ["risks if gaps not addressed"],
    "audienceImpact": ["how gaps affect audience value"],
    "competitiveRisks": ["risks compared to competitor content"],
    "mitigationStrategies": ["how to minimize risks"]
  },
  "successMetrics": {
    "completenessTarget": "target percentage after gap filling",
    "qualityImprovement": "expected quality enhancement",
    "researchROI": "value of additional research effort"
  }
}`,

  agent: superAgentTeam.gapAnalysisAgent
});

logKaibanTask('Gap Analysis Task created successfully');

/**
 * Task 5: Deep Research
 * Conduct targeted research to fill identified gaps
 */
logKaibanTask('Creating Deep Research Task', {
  taskNumber: 5,
  taskName: 'Deep Research',
  agent: 'Deep Researcher',
  dependsOn: 'Gap Analysis Task',
  expectedOutputs: ['deepResearchSummary', 'gapsAddressed', 'integratedFindings']
});

export const deepResearchTask = new Task({
  description: `As a Deep Research Specialist, execute targeted research to address priority gaps identified in: {taskResult:task4}

**DEEP RESEARCH METHODOLOGY**: Apply advanced research techniques with systematic gap-filling approach:

**Phase 1: Gap Prioritization and Research Planning**
- Analyze high and medium priority gaps from gap analysis
- Develop specialized search strategies for each gap category
- Plan research sequence to maximize efficiency and source quality
- Establish success criteria for gap resolution

**Phase 2: Advanced Google Search Execution**
- Execute targeted searches using Google Programmable Search Engine API
- Apply sophisticated search operators and techniques:
  * Boolean operators (AND, OR, NOT) for precision
  * Site-specific searches (site:edu, site:gov, site:expert-domains)
  * File type searches (filetype:pdf for research papers)
  * Date range filters for recent information (2024-2025)
  * Exact phrase searches for specific concepts
- Maximum results per gap: {maxDeepResults}
- Focus on authoritative and expert sources

**Phase 3: Expert Perspective and Authority Research**
- Target thought leaders, industry experts, and academic authorities
- Search for expert interviews, opinions, and commentary
- Locate authoritative publications and peer-reviewed sources
- Find expert social media insights and professional commentary
- Identify conference presentations and expert talks

**Phase 4: Evidence and Case Study Research**
- Search for supporting case studies and real-world examples
- Locate empirical evidence and research studies
- Find practical applications and implementation examples
- Gather success stories and failure analyses
- Collect quantitative data and statistical evidence

**Phase 5: Currency and Trend Research**
- Focus on 2024-2025 developments and emerging trends
- Search for recent news, updates, and industry developments
- Locate forward-looking predictions and trend analyses
- Find recent regulatory changes and industry shifts
- Identify emerging technologies and methodologies

**Phase 6: Cross-Validation and Verification**
- Cross-reference findings across multiple authoritative sources
- Validate claims and statistics through primary sources
- Identify consensus and conflicting viewpoints
- Assess information reliability and credibility
- Document source quality and verification status

**RESEARCH QUALITY STANDARDS**:
- Prioritize peer-reviewed and academically rigorous sources
- Focus on expert-authored content and authoritative publications
- Ensure geographic and demographic diversity when relevant
- Maintain high standards for source credibility and reliability
- Document research methodology and limitations

**INTEGRATION REQUIREMENTS**:
- Seamlessly integrate findings with existing primary research
- Resolve conflicts between primary and deep research findings
- Create comprehensive knowledge base for content generation
- Ensure all gaps are adequately addressed or documented as limitations

**OUTPUT REQUIREMENTS**: Provide comprehensive deep research results in structured JSON format:`,

  expectedOutput: `Comprehensive deep research results in JSON format with this exact structure:
{
  "researchSummary": {
    "totalGapsTargeted": "number of gaps addressed",
    "gapsFullyResolved": "number completely filled",
    "gapsPartiallyResolved": "number partially addressed",
    "newSourcesFound": "total new sources discovered",
    "researchDepth": "assessment of research thoroughness",
    "overallSuccess": "percentage of research objectives met"
  },
  "gapResolution": [
    {
      "gapId": "reference to original gap",
      "gapDescription": "what gap was being addressed",
      "resolutionStatus": "fully/partially/not resolved",
      "newInformation": "key information found",
      "sources": ["URLs and citations"],
      "qualityAssessment": "assessment of information quality",
      "remainingLimitations": "any remaining gaps or limitations"
    }
  ],
  "newResearchData": [
    {
      "title": "source title",
      "url": "source URL",
      "author": "author and credentials",
      "publication": "publication details",
      "publishDate": "publication date",
      "sourceType": "academic/expert/industry/government",
      "reliabilityScore": "1-10 with justification",
      "relevanceScore": "1-10 with justification",
      "gapAddressed": "which gap this source addresses",
      "keyContent": "extracted key information",
      "uniqueInsights": "new insights not found in primary research",
      "expertCredentials": "author expertise and authority"
    }
  ],
  "expertInsights": [
    {
      "expert": "expert name and credentials",
      "expertise": "area of specialization",
      "insight": "expert perspective or quote",
      "source": "where insight was found",
      "credibility": "assessment of expert authority",
      "relevance": "how insight addresses content needs",
      "uniqueness": "what makes this perspective valuable"
    }
  ],
  "evidenceAndCaseStudies": [
    {
      "type": "case study/research study/example",
      "title": "study or example title",
      "summary": "brief description",
      "keyFindings": ["main conclusions"],
      "methodology": "research approach if applicable",
      "source": "source attribution",
      "relevance": "how it supports content strategy",
      "credibility": "assessment of evidence quality"
    }
  ],
  "currentTrends": [
    {
      "trend": "trend description",
      "timeframe": "when trend is occurring",
      "source": "source of trend information",
      "implications": "what this means for the topic",
      "evidence": "supporting data or examples",
      "futureProjections": "where trend is heading"
    }
  ],
  "integratedFindings": {
    "combinedDataset": "summary of primary + deep research",
    "resolvedConflicts": ["conflicts resolved between sources"],
    "strengthenedAreas": ["areas where deep research reinforced primary findings"],
    "newDiscoveries": ["completely new information found"],
    "validatedClaims": ["claims confirmed through multiple sources"],
    "remainingUncertainties": ["areas still needing clarification"]
  },
  "researchQuality": {
    "sourceDistribution": {
      "academic": "percentage",
      "expert": "percentage",
      "industry": "percentage",
      "government": "percentage"
    },
    "averageReliability": "average reliability score",
    "informationRecency": "average age of information",
    "expertAuthorityLevel": "assessment of expert source quality",
    "crossValidation": "percentage of claims verified by multiple sources"
  },
  "contentReadiness": {
    "researchCompleteness": "percentage of content needs met",
    "informationDensity": "richness of available information",
    "perspectiveDiversity": "range of viewpoints covered",
    "evidenceStrength": "quality of supporting evidence",
    "practicalApplicability": "availability of practical examples",
    "readinessForGeneration": "high/medium/low readiness for content creation"
  },
  "researchLimitations": {
    "unresolvedGaps": ["gaps that couldn't be filled"],
    "sourceConstraints": ["limitations in available sources"],
    "accessRestrictions": ["information behind paywalls or restricted"],
    "conflictingInformation": ["areas with contradictory data"],
    "recommendedFutureResearch": ["suggestions for additional research"]
  }
}`,

  agent: superAgentTeam.deepResearchAgent
});

logKaibanTask('Deep Research Task created successfully');

/**
 * Task 6: Content Generation
 * Generate the final content based on all research and strategy
 */
logKaibanTask('Creating Content Generation Task', {
  taskNumber: 6,
  taskName: 'Content Generation',
  agent: 'Content Generator',
  dependsOn: ['Content Strategy Task', 'Primary Research Task', 'Deep Research Task'],
  expectedOutputs: ['title', 'content', 'wordCount', 'sourcesUsed']
});

export const contentGenerationTask = new Task({
  description: `As a Senior Content Creator, generate exceptional content for "{topic}" using comprehensive research and strategic framework:

**CONTENT CREATION FRAMEWORK**: Apply systematic content generation methodology:

**Input Analysis and Integration**:
- Content Strategy Framework: {taskResult:task2}
- Primary Research Foundation: {taskResult:task3}
- Deep Research Enhancement: {taskResult:task5}

**Content Specifications**:
- Content Type: {contentType}
- Target Word Count: {targetWordCount} (strict adherence required)
- Tone: {tone}
- Target Audience: {targetAudience}

**Phase 1: Content Architecture Implementation**
- Follow the exact content structure from the strategy framework
- Allocate word count precisely according to section planning
- Implement the narrative arc and logical progression
- Ensure seamless transitions between sections

**Phase 2: Research Integration and Evidence Weaving**
- Seamlessly integrate findings from both primary and deep research
- Incorporate statistics, expert quotes, and case studies naturally
- Balance theoretical insights with practical applications
- Ensure all claims are supported by credible sources

**Phase 3: Engagement and Value Creation**
- Implement planned hooks and attention-grabbing elements
- Weave in storytelling elements and real-world examples
- Create actionable insights and practical takeaways
- Design memorable and shareable content moments

**Phase 4: SEO and Discoverability Optimization**
- Integrate primary and secondary keywords naturally
- Optimize headings and subheadings for search engines
- Create compelling meta elements and descriptions
- Ensure content structure supports SEO objectives

**Phase 5: Quality and Readability Enhancement**
- Maintain consistent tone throughout the content
- Ensure appropriate reading level for target audience
- Create smooth flow and logical progression
- Implement engagement retention techniques

**Phase 6: Citation and Attribution**
- Properly attribute all sources and research findings
- Create comprehensive source list with proper formatting
- Ensure credibility through transparent sourcing
- Balance authority with readability

**CONTENT QUALITY STANDARDS**:
- Strictly adhere to target word count (no trimming afterward)
- Maintain exceptional readability and flow
- Ensure every paragraph adds unique value
- Create content that stands out from competitors
- Focus on 2025 trends and forward-looking perspectives
- Include tables and visual elements when beneficial

**CRITICAL SUCCESS FACTORS**:
- Content must be publication-ready without further editing
- All research must be seamlessly integrated, not just appended
- Tone must remain consistent with specifications throughout
- Word count must be precisely met during generation
- Content must provide exceptional value to target audience

**OUTPUT REQUIREMENTS**: Generate complete, publication-ready content in structured JSON format:`,

  expectedOutput: `Complete content package in JSON format with this exact structure:
{
  "contentMetadata": {
    "title": "engaging, SEO-optimized title",
    "subtitle": "compelling subtitle if applicable",
    "metaDescription": "SEO meta description (150-160 characters)",
    "focusKeyphrase": "primary SEO target keyword",
    "wordCount": "exact word count achieved",
    "estimatedReadingTime": "minutes",
    "contentType": "article/guide/analysis/etc",
    "targetAudience": "primary audience description",
    "tone": "tone maintained throughout"
  },
  "content": "FULL CONTENT IN MARKDOWN FORMAT - This should be the complete, publication-ready article with proper markdown formatting, headings, and structure",
  "contentStructure": {
    "outline": [
      {
        "section": "section title",
        "level": "H1/H2/H3",
        "wordCount": "words in this section",
        "keyPoints": ["main points covered"]
      }
    ],
    "introduction": {
      "hook": "opening hook used",
      "valueProposition": "what readers will gain",
      "roadmap": "content preview"
    },
    "mainSections": [
      {
        "title": "section title",
        "wordCount": "section word count",
        "keyMessages": ["messages conveyed"],
        "researchIntegrated": ["research sources used"],
        "engagementElements": ["hooks, examples, stories used"]
      }
    ],
    "conclusion": {
      "summary": "key points summarized",
      "callToAction": "action for readers",
      "memorableClosing": "final impression"
    }
  },
  "researchIntegration": {
    "sourcesUsed": [
      {
        "source": "source title and URL",
        "type": "statistic/quote/case study/example",
        "placement": "where used in content",
        "purpose": "why included",
        "credibility": "source authority level"
      }
    ],
    "statisticsIncluded": [
      {
        "statistic": "numerical data",
        "source": "attribution",
        "context": "how presented in content"
      }
    ],
    "expertQuotes": [
      {
        "quote": "expert statement",
        "expert": "expert name and credentials",
        "source": "source attribution",
        "placement": "where used in content"
      }
    ],
    "caseStudies": [
      {
        "caseStudy": "case study title",
        "summary": "brief description",
        "keyLessons": ["takeaways"],
        "placement": "where used in content"
      }
    ]
  },
  "seoOptimization": {
    "primaryKeywords": ["main SEO targets with frequency"],
    "secondaryKeywords": ["supporting terms with frequency"],
    "keywordDensity": "overall keyword density percentage",
    "headingOptimization": ["H1, H2, H3 tags with keywords"],
    "internalLinkingOpportunities": ["suggested internal links"],
    "featuredSnippetOptimization": "content optimized for featured snippets"
  },
  "engagementElements": {
    "hooks": ["attention-grabbing elements used"],
    "storytellingElements": ["narratives and stories included"],
    "practicalExamples": ["real-world applications"],
    "actionableInsights": ["specific takeaways for readers"],
    "interactiveElements": ["questions, exercises, tools mentioned"],
    "visualElements": ["suggested charts, images, infographics"],
    "shareableQuotes": ["memorable quotes for social sharing"]
  },
  "qualityMetrics": {
    "readabilityScore": "Flesch-Kincaid or similar score",
    "sentenceVariety": "assessment of sentence structure diversity",
    "paragraphFlow": "assessment of logical progression",
    "toneConsistency": "evaluation of tone maintenance",
    "valueDelivery": "assessment of reader value provided",
    "uniqueness": "differentiation from competitor content",
    "completeness": "coverage of topic requirements"
  },
  "keyTakeaways": [
    "main insight 1 with supporting evidence",
    "main insight 2 with supporting evidence",
    "main insight 3 with supporting evidence",
    "actionable recommendation 1",
    "actionable recommendation 2"
  ],
  "contentValidation": {
    "wordCountAccuracy": "exact match to target",
    "strategyAlignment": "adherence to content strategy",
    "researchIntegration": "seamless incorporation of research",
    "audienceAppropriate": "suitability for target audience",
    "toneConsistency": "maintenance of specified tone",
    "seoCompliance": "optimization for search engines",
    "publicationReadiness": "ready for immediate publication"
  }
}`,

  agent: superAgentTeam.contentGenerationAgent
});

logKaibanTask('Content Generation Task created successfully');

/**
 * Task 7: Quality Assurance
 * Review and validate the generated content
 */
logKaibanTask('Creating Quality Assurance Task', {
  taskNumber: 7,
  taskName: 'Quality Assurance',
  agent: 'Quality Assurance',
  dependsOn: ['Content Generation Task', 'Content Strategy Task', 'Research Tasks'],
  expectedOutputs: ['overallQualityScore', 'detailedFeedback', 'finalApproval']
});

export const qualityAssuranceTask = new Task({
  description: `As a Quality Assurance Director, conduct comprehensive quality evaluation of the generated content using advanced reasoning and systematic methodology.

**QUALITY ASSURANCE FRAMEWORK**: Apply rigorous evaluation standards using chain-of-thought analysis:

**Content Under Review**: {taskResult:task6}

**Evaluation Benchmarks**:
- Original Topic: "{topic}"
- Content Strategy Requirements: {taskResult:task2}
- Primary Research Foundation: {taskResult:task3}
- Deep Research Enhancement: {taskResult:task5}
- Content Specifications: {contentType}, {targetWordCount} words, {tone} tone, {targetAudience} audience

**Phase 1: Strategic Alignment Assessment**
- Verify content follows the exact structure from content strategy
- Confirm all key messages are effectively communicated
- Assess alignment with target audience needs and preferences
- Evaluate adherence to specified tone and content type requirements
- Check word count accuracy and section distribution

**Phase 2: Research Integration Evaluation**
- Verify seamless integration of primary and deep research findings
- Assess accuracy of statistics, quotes, and factual claims
- Evaluate proper attribution and source credibility
- Check for balanced use of different types of evidence
- Confirm all research gaps have been adequately addressed

**Phase 3: Content Quality and Readability Analysis**
- Assess clarity, coherence, and logical flow
- Evaluate sentence structure variety and paragraph transitions
- Check reading level appropriateness for target audience
- Assess engagement value and reader retention elements
- Evaluate professional presentation and polish

**Phase 4: SEO and Discoverability Evaluation**
- Verify natural integration of primary and secondary keywords
- Assess heading structure and SEO optimization
- Evaluate meta elements and search engine friendliness
- Check keyword density and distribution
- Assess potential for search engine ranking

**Phase 5: Engagement and Value Assessment**
- Evaluate effectiveness of hooks and attention-grabbing elements
- Assess storytelling elements and example integration
- Check actionable insights and practical takeaways
- Evaluate shareability and memorability factors
- Assess overall reader value and satisfaction potential

**Phase 6: Technical and Editorial Review**
- Check grammar, spelling, and punctuation accuracy
- Assess formatting consistency and markdown structure
- Evaluate citation format and source attribution
- Check for factual errors or inconsistencies
- Assess overall technical presentation quality

**Phase 7: Competitive and Market Analysis**
- Evaluate content differentiation from competitors
- Assess unique value proposition and positioning
- Check for industry best practices compliance
- Evaluate potential market impact and reception
- Assess publication readiness and market fit

**QUALITY STANDARDS**:
- Content must meet or exceed industry benchmarks
- All factual claims must be verifiable and accurate
- Content must provide exceptional value to target audience
- SEO optimization must be natural and effective
- Content must be immediately publication-ready

**CRITICAL EVALUATION CRITERIA**:
- Zero tolerance for factual inaccuracies
- Strict adherence to word count and specification requirements
- Exceptional readability and engagement standards
- Professional presentation and technical quality
- Strategic alignment and objective achievement

**OUTPUT REQUIREMENTS**: Provide comprehensive quality assessment in structured JSON format:`,

  expectedOutput: `Comprehensive quality assurance report in JSON format with this exact structure:
{
  "executiveSummary": {
    "overallQualityScore": "1-100 comprehensive score",
    "publicationReadiness": "ready/needs minor revisions/needs major revisions",
    "finalApproval": "boolean - true if approved for publication",
    "keyStrengths": ["top 3-5 content strengths"],
    "criticalIssues": ["any issues requiring immediate attention"],
    "recommendationSummary": "overall recommendation"
  },
  "strategicAlignment": {
    "strategyComplianceScore": "1-100 score",
    "structureAdherence": "assessment of structure following",
    "keyMessageDelivery": "evaluation of message communication",
    "audienceAlignment": "suitability for target audience",
    "toneConsistency": "maintenance of specified tone",
    "wordCountAccuracy": "exact vs target word count",
    "contentTypeAppropriate": "suitability for specified content type"
  },
  "researchIntegration": {
    "researchIntegrationScore": "1-100 score",
    "factualAccuracy": "verification of claims and statistics",
    "sourceCredibility": "assessment of source quality and attribution",
    "evidenceBalance": "variety and balance of evidence types",
    "gapResolution": "how well research gaps were addressed",
    "citationQuality": "proper attribution and formatting",
    "dataAccuracy": "verification of numerical data and statistics"
  },
  "contentQuality": {
    "contentQualityScore": "1-100 score",
    "clarity": "assessment of clear communication",
    "coherence": "logical flow and structure",
    "readability": "appropriate reading level and flow",
    "engagement": "ability to capture and maintain attention",
    "professionalism": "professional presentation quality",
    "uniqueness": "differentiation from existing content",
    "valueDelivery": "practical value provided to readers"
  },
  "seoOptimization": {
    "seoScore": "1-100 score",
    "keywordIntegration": "natural keyword usage assessment",
    "headingOptimization": "SEO-friendly heading structure",
    "metaElements": "quality of title and description",
    "searchability": "potential for search engine ranking",
    "keywordDensity": "appropriate keyword frequency",
    "competitivePositioning": "SEO advantage over competitors"
  },
  "engagementAnalysis": {
    "engagementScore": "1-100 score",
    "hookEffectiveness": "quality of attention-grabbing elements",
    "storytellingQuality": "narrative elements and examples",
    "practicalValue": "actionable insights and takeaways",
    "shareability": "potential for social sharing",
    "memorability": "likelihood of reader retention",
    "callToActionEffectiveness": "quality of reader guidance"
  },
  "technicalQuality": {
    "technicalScore": "1-100 score",
    "grammarAccuracy": "grammar and language quality",
    "formattingConsistency": "markdown and structure formatting",
    "citationFormat": "proper source attribution format",
    "structuralIntegrity": "heading hierarchy and organization",
    "presentationPolish": "overall technical presentation"
  },
  "detailedFeedback": {
    "strengths": [
      {
        "area": "specific strength area",
        "description": "detailed explanation",
        "impact": "why this is valuable",
        "examples": "specific examples from content"
      }
    ],
    "improvementAreas": [
      {
        "area": "area needing improvement",
        "issue": "specific problem identified",
        "impact": "why this matters",
        "recommendation": "specific improvement suggestion",
        "priority": "high/medium/low"
      }
    ],
    "factualVerification": [
      {
        "claim": "factual claim made",
        "verification": "verified/needs verification/inaccurate",
        "source": "supporting source",
        "confidence": "confidence level in accuracy"
      }
    ]
  },
  "competitiveAnalysis": {
    "competitiveAdvantage": "how content beats competitors",
    "uniqueValueProposition": "what makes this content special",
    "marketPositioning": "position in content landscape",
    "differentiationFactors": ["specific differentiation elements"]
  },
  "publicationRecommendations": {
    "immediateActions": ["actions needed before publication"],
    "optimizationSuggestions": ["ways to enhance content further"],
    "distributionStrategy": ["recommended publication channels"],
    "performanceExpectations": ["expected content performance"],
    "monitoringMetrics": ["metrics to track post-publication"]
  },
  "qualityMetrics": {
    "readabilityGrade": "reading level assessment",
    "sentenceComplexity": "sentence structure analysis",
    "vocabularyAppropriate": "vocabulary level assessment",
    "contentDensity": "information density evaluation",
    "flowQuality": "transition and progression quality",
    "professionalStandard": "industry standard compliance"
  },
  "finalAssessment": {
    "publicationApproval": "approved/conditional/rejected",
    "confidenceLevel": "1-10 confidence in quality assessment",
    "expectedPerformance": "predicted content success level",
    "recommendedNextSteps": ["specific actions to take"],
    "qualityBenchmark": "how content compares to industry standards"
  }
}`,

  agent: superAgentTeam.qualityAssuranceAgent
});

logKaibanTask('Quality Assurance Task created successfully');

/**
 * Export all tasks in execution order
 */
logKaibanTask('Exporting Super Agent Tasks', {
  totalTasks: 7,
  executionOrder: [
    'Topic Analysis',
    'Content Strategy',
    'Primary Research',
    'Gap Analysis',
    'Deep Research',
    'Content Generation',
    'Quality Assurance'
  ]
});

export const superAgentTasks = [
  topicAnalysisTask,
  contentStrategyTask,
  primaryResearchTask,
  gapAnalysisTask,
  deepResearchTask,
  contentGenerationTask,
  qualityAssuranceTask
];

/**
 * Task configuration for easy reference
 */
logKaibanTask('Creating Task Configuration', {
  totalTasks: 7,
  configurationComplete: true
});

export const taskConfig = {
  tasks: superAgentTasks,
  taskNames: [
    'Topic Analysis',
    'Content Strategy',
    'Primary Research',
    'Gap Analysis',
    'Deep Research',
    'Content Generation',
    'Quality Assurance'
  ],
  totalTasks: superAgentTasks.length
};

logKaibanTask('Kaiban Task System initialization complete', {
  status: 'ready',
  totalTasks: 7,
  sequentialExecution: true,
  taskDependencies: {
    'Content Strategy': ['Topic Analysis'],
    'Primary Research': ['Topic Analysis'],
    'Gap Analysis': ['Primary Research', 'Content Strategy'],
    'Deep Research': ['Gap Analysis'],
    'Content Generation': ['Content Strategy', 'Primary Research', 'Deep Research'],
    'Quality Assurance': ['Content Generation', 'Content Strategy', 'Research Tasks']
  },
  timestamp: new Date().toISOString()
});
