/**
 * KaibanJS Super Agent System - Main Export
 * 
 * This file exports all the components of the KaibanJS-based super agent system
 * for easy import and use throughout the application.
 */

// Export tools
export {
  GoogleSearchTool,
  ContentAnalysisTool,
  ContentGenerationTool,
  DataProcessingTool
} from './tools';

// Export agents
export {
  superAgentTeam,
  agentConfig,
  topicAnalysisAgent,
  contentStrategyAgent,
  primaryResearchAgent,
  gapAnalysisAgent,
  deepResearchAgent,
  contentGenerationAgent,
  qualityAssuranceAgent
} from './agents';

// Export tasks
export {
  superAgentTasks,
  taskConfig,
  topicAnalysisTask,
  contentStrategyTask,
  primaryResearchTask,
  gapAnalysisTask,
  deepResearchTask,
  contentGenerationTask,
  qualityAssuranceTask
} from './tasks';

// Import types and functions for internal use
import type { SuperAgentOptions, SuperAgentResult } from './super-agent-team';
import { executeSuperAgentWorkflow } from './super-agent-team';

// Export team and workflow
export {
  createSuperAgentTeam,
  executeSuperAgentWorkflow
} from './super-agent-team';

// Export types
export type {
  SuperAgentOptions,
  SuperAgentResult
} from './super-agent-team';

// Export default team creator
export { default as KaibanSuperAgentTeam } from './super-agent-team';

/**
 * KaibanJS Super Agent System Information
 */
export const KAIBAN_SUPER_AGENT_INFO = {
  name: 'KaibanJS Super Agent System',
  version: '1.0.0',
  description: 'A comprehensive multi-agent content creation system built with KaibanJS',
  
  agents: {
    count: 7,
    names: [
      'Topic Analyzer - Topic Analysis Specialist',
      'Content Strategist - Content Strategy Director',
      'Primary Researcher - Primary Research Specialist',
      'Gap Analyst - Research Gap Analyst',
      'Deep Researcher - Deep Research Specialist',
      'Content Generator - Senior Content Creator',
      'Quality Assurance - Quality Assurance Director'
    ]
  },
  
  workflow: {
    phases: 7,
    steps: [
      'Topic Analysis',
      'Content Strategy Development',
      'Primary Research',
      'Gap Analysis',
      'Deep Research',
      'Content Generation',
      'Quality Assurance'
    ]
  },
  
  capabilities: [
    'Comprehensive topic analysis',
    'Strategic content planning',
    'Multi-source research',
    'Gap identification and filling',
    'High-quality content generation',
    'Automated quality assurance',
    'Real-time progress tracking',
    'Source attribution and citations'
  ],
  
  supportedContentTypes: [
    'article',
    'blog-post', 
    'research-paper',
    'comprehensive-guide'
  ],
  
  supportedTones: [
    'professional',
    'casual',
    'academic',
    'conversational'
  ],
  
  supportedAudiences: [
    'beginner',
    'intermediate',
    'expert',
    'general'
  ]
};

/**
 * Quick Start Function
 * Provides a simple way to execute the workflow with minimal configuration
 */
export async function quickStartSuperAgent(
  topic: string,
  options?: Partial<SuperAgentOptions>,
  onProgress?: (phase: string, progress: number, message: string) => void
) {
  const defaultOptions: SuperAgentOptions = {
    topic,
    contentType: 'article',
    targetWordCount: 2000,
    tone: 'professional',
    targetAudience: 'intermediate',
    maxPrimaryResults: 6,
    maxDeepResults: 4,
    enableFactChecking: true,
    includeSourceCitations: true,
    ...options
  };
  
  return await executeSuperAgentWorkflow(defaultOptions, onProgress);
}

/**
 * Utility Functions
 */

/**
 * Validate Super Agent Options
 */
export function validateSuperAgentOptions(options: SuperAgentOptions): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!options.topic || options.topic.trim().length === 0) {
    errors.push('Topic is required and cannot be empty');
  }
  
  if (options.topic && options.topic.length < 3) {
    errors.push('Topic must be at least 3 characters long');
  }
  
  if (options.targetWordCount && (options.targetWordCount < 100 || options.targetWordCount > 10000)) {
    errors.push('Target word count must be between 100 and 10,000');
  }
  
  if (options.maxPrimaryResults && (options.maxPrimaryResults < 1 || options.maxPrimaryResults > 20)) {
    errors.push('Max primary results must be between 1 and 20');
  }
  
  if (options.maxDeepResults && (options.maxDeepResults < 1 || options.maxDeepResults > 10)) {
    errors.push('Max deep results must be between 1 and 10');
  }
  
  const validContentTypes = ['article', 'blog-post', 'research-paper', 'comprehensive-guide'];
  if (options.contentType && !validContentTypes.includes(options.contentType)) {
    errors.push(`Content type must be one of: ${validContentTypes.join(', ')}`);
  }
  
  const validTones = ['professional', 'casual', 'academic', 'conversational'];
  if (options.tone && !validTones.includes(options.tone)) {
    errors.push(`Tone must be one of: ${validTones.join(', ')}`);
  }
  
  const validAudiences = ['beginner', 'intermediate', 'expert', 'general'];
  if (options.targetAudience && !validAudiences.includes(options.targetAudience)) {
    errors.push(`Target audience must be one of: ${validAudiences.join(', ')}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get Estimated Execution Time
 */
export function getEstimatedExecutionTime(options: SuperAgentOptions): {
  estimatedMinutes: number;
  breakdown: { [phase: string]: number };
} {
  const baseTime = 2; // Base time per phase in minutes
  const researchMultiplier = (options.maxPrimaryResults || 6) * 0.5 + (options.maxDeepResults || 4) * 0.8;
  const wordCountMultiplier = (options.targetWordCount || 2000) / 1000;
  
  const breakdown = {
    'Topic Analysis': baseTime,
    'Content Strategy': baseTime,
    'Primary Research': baseTime + researchMultiplier,
    'Gap Analysis': baseTime * 0.8,
    'Deep Research': baseTime + (researchMultiplier * 0.6),
    'Content Generation': baseTime + wordCountMultiplier,
    'Quality Assurance': baseTime * 0.8
  };
  
  const estimatedMinutes = Object.values(breakdown).reduce((sum, time) => sum + time, 0);
  
  return {
    estimatedMinutes: Math.round(estimatedMinutes),
    breakdown
  };
}

/**
 * Progress Tracking Utilities
 */
export const WORKFLOW_PHASES = [
  'topic-analysis',
  'content-strategy', 
  'primary-research',
  'gap-analysis',
  'deep-research',
  'content-generation',
  'quality-assurance'
] as const;

export type WorkflowPhase = typeof WORKFLOW_PHASES[number];

export function getPhaseDisplayName(phase: WorkflowPhase): string {
  const displayNames: { [key in WorkflowPhase]: string } = {
    'topic-analysis': 'Topic Analysis',
    'content-strategy': 'Content Strategy',
    'primary-research': 'Primary Research', 
    'gap-analysis': 'Gap Analysis',
    'deep-research': 'Deep Research',
    'content-generation': 'Content Generation',
    'quality-assurance': 'Quality Assurance'
  };
  
  return displayNames[phase];
}

export function getPhaseProgress(phase: WorkflowPhase): number {
  const phaseIndex = WORKFLOW_PHASES.indexOf(phase);
  return Math.round(((phaseIndex + 1) / WORKFLOW_PHASES.length) * 100);
}
