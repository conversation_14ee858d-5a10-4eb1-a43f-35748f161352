/**
 * KaibanJS Tools for Super Agent Workflow
 *
 * This file contains custom tools that will be used by KaibanJS agents
 * to perform research, content generation, and quality assurance tasks.
 */

import { Tool } from "@langchain/core/tools";
import { TavilySearchResults } from '@kaibanjs/tools';
import { GeminiService } from "../../gemini";

/**
 * Tavily Search Tool for KaibanJS
 * Performs web searches using Tavily AI-powered search engine
 */
export class TavilySearchTool extends TavilySearchResults {
  constructor() {
    super({
      apiKey: process.env.TAVILY_API_KEY,
      maxResults: 5
    });

    console.log(`🔍 [TavilySearchTool] Initialized with API key: ${process.env.TAVILY_API_KEY ? 'Present' : 'Missing'}`);
  }
}

/**
 * Content Analysis Tool for KaibanJS
 * Analyzes content using Gemini AI
 */
export class ContentAnalysisTool extends Tool {
  name = "content_analysis";
  description = "Analyze content using Gemini AI for insights, keywords, and structure. Input should be JSON string with content, analysisType (topic/gap/quality), and optional context.";

  private geminiService: GeminiService;

  constructor() {
    super();
    this.geminiService = new GeminiService();
  }

  async _call(input: string): Promise<string> {
    try {
      const parsedInput = JSON.parse(input);
      const { content, analysisType, context } = parsedInput;
    try {
      let prompt = "";

      switch (analysisType) {
        case "topic":
          prompt = `Analyze the following topic and provide detailed insights:
Topic: ${content}
${context ? `Context: ${context}` : ''}

Please provide:
1. Main topic and subtopics
2. Key terms and semantic keywords
3. Research queries to explore
4. Content angles and perspectives
5. Target audience analysis
6. Content complexity level

Format your response as JSON.`;
          break;

        case "gap":
          prompt = `Analyze the following research data for gaps and missing information:
Research Data: ${content}
${context ? `Context: ${context}` : ''}

Please identify:
1. Missing information or gaps
2. Areas needing deeper research
3. Conflicting information
4. Additional research queries needed
5. Quality assessment of current data

Format your response as JSON.`;
          break;

        case "quality":
          prompt = `Perform quality assurance on the following content:
Content: ${content}
${context ? `Context: ${context}` : ''}

Please evaluate:
1. Factual accuracy and consistency
2. Readability and clarity
3. Completeness and comprehensiveness
4. Source reliability
5. Overall quality score (1-100)
6. Specific improvement recommendations

Format your response as JSON.`;
          break;
      }

      const result = await this.geminiService.generateContent(prompt);
      return result;
    } catch (error) {
      return `Error analyzing content: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

/**
 * Content Generation Tool for KaibanJS
 * Generates content using Gemini AI
 */
export class ContentGenerationTool extends Tool {
  name = "content_generation";
  description = "Generate high-quality content using Gemini AI. Input should be JSON string with topic, researchData, contentType, targetWordCount, tone, and targetAudience.";

  private geminiService: GeminiService;

  constructor() {
    super();
    this.geminiService = new GeminiService();
  }

  async _call(input: string): Promise<string> {
    try {
      const parsedInput = JSON.parse(input);
      const { topic, researchData, contentType, targetWordCount = 2000, tone, targetAudience } = parsedInput;
    try {
      const prompt = `Generate a high-quality ${contentType} on the following topic:

Topic: ${topic}
Target Word Count: ${targetWordCount} words
Tone: ${tone}
Target Audience: ${targetAudience}

Research Data to incorporate:
${researchData}

Requirements:
1. Create an engaging title
2. Structure the content with clear headings and subheadings
3. Include relevant examples and case studies from the research data
4. Maintain the specified tone throughout
5. Ensure the content is appropriate for the target audience
6. Include actionable insights and takeaways
7. Add a compelling conclusion
8. Aim for approximately ${targetWordCount} words

Please format the response as JSON with the following structure:
{
  "title": "Generated title",
  "content": "Full article content in markdown format",
  "wordCount": actual_word_count,
  "outline": ["heading1", "heading2", ...],
  "keyPoints": ["point1", "point2", ...],
  "sources": ["source1", "source2", ...]
}`;

      const result = await this.geminiService.generateContent(prompt);
      return result;
    } catch (error) {
      return `Error generating content: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }
}

/**
 * Data Processing Tool for KaibanJS
 * Processes and structures data for agent communication
 */
export class DataProcessingTool extends Tool {
  name = "data_processing";
  description = "Process and structure data for inter-agent communication. Input should be JSON string with data, operation (merge/filter/summarize/structure), and optional criteria.";

  async _call(input: string): Promise<string> {
    try {
      const parsedInput = JSON.parse(input);
      const { data, operation, criteria } = parsedInput;
    try {
      let processedData: any;

      switch (operation) {
        case "merge":
          // Merge multiple data sources
          processedData = this.mergeData(data);
          break;

        case "filter":
          // Filter data based on criteria
          processedData = this.filterData(data, criteria || "");
          break;

        case "summarize":
          // Summarize data
          processedData = this.summarizeData(data);
          break;

        case "structure":
          // Structure data into a consistent format
          processedData = this.structureData(data);
          break;
      }

      return JSON.stringify(processedData);
    } catch (error) {
      return `Error processing data: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  private mergeData(data: string): any {
    try {
      const parsedData = JSON.parse(data);
      if (Array.isArray(parsedData)) {
        return parsedData.reduce((merged, item) => ({ ...merged, ...item }), {});
      }
      return parsedData;
    } catch {
      return { mergedData: data };
    }
  }

  private filterData(data: string, criteria: string): any {
    try {
      const parsedData = JSON.parse(data);
      if (Array.isArray(parsedData)) {
        return parsedData.filter(item => 
          JSON.stringify(item).toLowerCase().includes(criteria.toLowerCase())
        );
      }
      return parsedData;
    } catch {
      return { filteredData: data };
    }
  }

  private summarizeData(data: string): any {
    try {
      const parsedData = JSON.parse(data);
      return {
        summary: "Data processed and summarized",
        itemCount: Array.isArray(parsedData) ? parsedData.length : 1,
        keys: typeof parsedData === 'object' ? Object.keys(parsedData) : [],
        data: parsedData
      };
    } catch {
      return { summary: data.substring(0, 200) + "..." };
    }
  }

  private structureData(data: string): any {
    try {
      const parsedData = JSON.parse(data);
      return {
        structured: true,
        timestamp: new Date().toISOString(),
        data: parsedData
      };
    } catch {
      return {
        structured: true,
        timestamp: new Date().toISOString(),
        data: { rawData: data }
      };
    }
  }
}
