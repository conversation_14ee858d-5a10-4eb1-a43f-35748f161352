/**
 * Primary Research Agent - Conducts initial research and data gathering
 */

import { BaseAgent } from './base-agent';
import { AgentContext, ResearchData, AgentCapabilities } from './types';
import { AgentUtils } from './shared-services';

export class PrimaryResearchAgent extends BaseAgent {
  constructor() {
    const capabilities: AgentCapabilities = {
      canRunInParallel: false,
      dependencies: ['TopicAnalysisAgent'],
      estimatedExecutionTime: 60000, // 60 seconds
      retryable: true,
      criticalPath: true
    };

    super('PrimaryResearchAgent', 'primary_research', capabilities);
  }

  protected async executeCore(context: AgentContext): Promise<ResearchData[]> {
    this.updateProgress(context, 'Conducting primary research...', 30);

    try {
      const topicAnalysis = context.state.topicAnalysis;
      if (!topicAnalysis) {
        throw new Error('Topic analysis not available');
      }

      const maxResults = context.state.options.maxPrimaryResults || 6;
      const primaryResearch: ResearchData[] = [];

      // Step 1: Exact topic search
      this.updateProgress(context, 'Searching for exact topic information...', 35);
      await this.performExactTopicSearch(context, topicAnalysis, primaryResearch, maxResults);

      // Step 2: Research queries from topic analysis
      this.updateProgress(context, 'Executing targeted research queries...', 40);
      await this.performTargetedResearch(context, topicAnalysis, primaryResearch, maxResults);

      // Step 3: Additional queries based on initial results
      this.updateProgress(context, 'Analyzing results for additional research directions...', 45);
      await this.performAdditionalResearch(context, primaryResearch, maxResults);

      // Process and rank results
      const processedResearch = this.processResearchResults(primaryResearch, context.state.topic);
      
      // Update state
      context.state.primaryResearch = processedResearch;
      this.addResult(context, processedResearch);
      
      this.updateProgress(context, `Primary research completed with ${processedResearch.length} sources`, 50);
      
      context.services.logger.info('Primary research completed', {
        sourcesFound: processedResearch.length,
        avgRelevanceScore: this.calculateAverageRelevance(processedResearch),
        sourceTypes: this.getSourceTypeDistribution(processedResearch)
      });

      return processedResearch;

    } catch (error) {
      const errorMessage = `Primary research failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      this.addError(context, errorMessage);
      
      // Return empty results
      context.state.primaryResearch = [];
      this.addResult(context, []);
      
      this.updateProgress(context, 'Primary research completed with errors', 50);
      return [];
    }
  }

  private async performExactTopicSearch(
    context: AgentContext,
    topicAnalysis: any,
    primaryResearch: ResearchData[],
    maxResults: number
  ): Promise<void> {
    // Enhanced search strategy inspired by GPT-Researcher
    const searchQueries = [
      topicAnalysis.mainTopic || context.state.topic,
      `${topicAnalysis.mainTopic} comprehensive guide`,
      `${topicAnalysis.mainTopic} expert analysis`,
      `${topicAnalysis.mainTopic} research study`
    ];

    for (const query of searchQueries.slice(0, 2)) {
      if (primaryResearch.length >= maxResults) break;

      try {
        const searchResults = await context.services.search.searchAndExtract(
          query,
          Math.min(5, Math.ceil(maxResults / 2))
        );

        if (searchResults?.extractedContent?.length) {
          const processedResults = AgentUtils.processSearchResults(
            searchResults,
            topicAnalysis.keyTerms,
            'exact-search',
            this.name
          );

          // Enhanced filtering based on relevance and quality
          const filteredResults = this.filterHighQualityResults(processedResults, topicAnalysis);
          primaryResearch.push(...filteredResults);
        }
      } catch (error) {
        context.services.logger.error(`Exact search error for query "${query}"`, error);
        continue;
      }
    }
  }

  private filterHighQualityResults(results: ResearchData[], topicAnalysis: any): ResearchData[] {
    return results
      .filter(result => {
        // Filter out low-quality sources
        const domain = result.metadata.domain.toLowerCase();
        const excludeDomains = ['pinterest.com', 'instagram.com', 'twitter.com', 'facebook.com'];
        if (excludeDomains.some(excluded => domain.includes(excluded))) return false;

        // Require minimum content length
        if (result.content.length < 200) return false;

        // Require minimum relevance score
        if (result.relevanceScore < 0.3) return false;

        return true;
      })
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 3); // Limit to top 3 per query
  }

  private async performTargetedResearch(
    context: AgentContext,
    topicAnalysis: any,
    primaryResearch: ResearchData[],
    maxResults: number
  ): Promise<void> {
    const researchQueries = topicAnalysis.researchQueries?.slice(0, 3) || [`${context.state.topic} guide`];

    for (const query of researchQueries) {
      if (primaryResearch.length >= maxResults) break;

      try {
        const queryResults = await context.services.search.searchAndExtract(query, 3);
        if (queryResults?.extractedContent?.length) {
          const processedResults = AgentUtils.processSearchResults(
            queryResults, 
            topicAnalysis.keyTerms, 
            'research-query',
            this.name
          );
          primaryResearch.push(...processedResults.slice(0, 2));
        }
      } catch (error) {
        context.services.logger.error(`Research query error for "${query}"`, error);
        continue;
      }
    }
  }

  private async performAdditionalResearch(
    context: AgentContext,
    primaryResearch: ResearchData[],
    maxResults: number
  ): Promise<void> {
    if (primaryResearch.length === 0) return;

    try {
      const additionalQueries = await this.generateAdditionalQueries(context, primaryResearch.slice(0, 3));

      for (const query of additionalQueries.slice(0, 2)) {
        if (primaryResearch.length >= maxResults) break;

        try {
          const additionalResults = await context.services.search.searchAndExtract(query, 2);
          if (additionalResults?.extractedContent?.length) {
            const processedResults = AgentUtils.processSearchResults(
              additionalResults, 
              context.state.topicAnalysis?.keyTerms || [], 
              'additional-query',
              this.name
            );
            primaryResearch.push(...processedResults.slice(0, 1));
          }
        } catch (error) {
          context.services.logger.error(`Additional query error for "${query}"`, error);
          continue;
        }
      }
    } catch (error) {
      context.services.logger.error('Additional research generation error', error);
    }
  }

  private async generateAdditionalQueries(context: AgentContext, topResults: ResearchData[]): Promise<string[]> {
    try {
      const contentSummaries = topResults.map(result =>
        `Title: ${result.title}\nContent: ${AgentUtils.sanitizeContent(result.content, 500)}`
      ).join('\n\n');

      const queryPrompt = `
Based on the topic "${context.state.topic}" and these top search results, generate 3 additional specific search queries that would find missing information or different perspectives:

${contentSummaries}

Generate queries that would find:
1. Expert opinions or analysis
2. Recent developments or updates
3. Comparative or alternative viewpoints

Return only the search queries, one per line.
      `;

      const queryResult = await context.services.gemini.generateContent(queryPrompt, {
        temperature: 0.5,
        maxOutputTokens: 500
      });

      return queryResult.split('\n')
        .filter((line: string) => line.trim().length > 0)
        .map((line: string) => line.replace(/^\d+\.\s*/, '').trim())
        .slice(0, 3);
    } catch (error) {
      context.services.logger.error('Additional query generation error', error);
      return [`${context.state.topic} expert analysis`, `${context.state.topic} latest developments`];
    }
  }

  private processResearchResults(research: ResearchData[], topic: string): ResearchData[] {
    // Deduplicate results
    const uniqueResearch = AgentUtils.deduplicateResearch(research);
    
    // Rank by relevance and quality
    const rankedResearch = AgentUtils.rankResearchByRelevance(uniqueResearch);
    
    // Validate and filter results
    const validResearch = rankedResearch.filter(item => AgentUtils.validateResearchData(item));
    
    return validResearch.slice(0, 10); // Limit to top 10 results
  }

  private calculateAverageRelevance(research: ResearchData[]): number {
    if (research.length === 0) return 0;
    const total = research.reduce((sum, item) => sum + item.relevanceScore, 0);
    return Math.round((total / research.length) * 100) / 100;
  }

  private getSourceTypeDistribution(research: ResearchData[]): { [key: string]: number } {
    const distribution: { [key: string]: number } = {};
    
    research.forEach(item => {
      const type = item.metadata.sourceType || 'unknown';
      distribution[type] = (distribution[type] || 0) + 1;
    });
    
    return distribution;
  }

  async validate(context: AgentContext): Promise<boolean> {
    // Check if topic analysis is available
    if (!context.state.topicAnalysis) {
      context.services.logger.error('PrimaryResearchAgent: Topic analysis not available');
      return false;
    }

    if (!context.services.search) {
      context.services.logger.error('PrimaryResearchAgent: Search service not available');
      return false;
    }

    if (!context.services.gemini) {
      context.services.logger.error('PrimaryResearchAgent: Gemini service not available');
      return false;
    }

    return true;
  }
}
