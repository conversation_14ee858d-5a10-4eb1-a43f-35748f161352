/**
 * Quality Assurance Agent - Performs quality checks and fact verification
 */

import { BaseAgent } from './base-agent';
import { AgentContext, QualityMetrics, SourceCitation, FactCheckResults, AgentCapabilities } from './types';

export class QualityAssuranceAgent extends BaseAgent {
  constructor() {
    const capabilities: AgentCapabilities = {
      canRunInParallel: false,
      dependencies: ['ContentGenerationAgent'],
      estimatedExecutionTime: 30000, // 30 seconds
      retryable: true,
      criticalPath: false
    };

    super('QualityAssuranceAgent', 'quality_assurance', capabilities);
  }

  protected async executeCore(context: AgentContext): Promise<{
    qualityMetrics: QualityMetrics;
    citations: SourceCitation[];
    factCheckResults?: FactCheckResults;
  }> {
    this.updateProgress(context, 'Performing quality assurance...', 98);

    try {
      const content = context.state.content;
      const knowledgeBase = context.state.knowledgeBase;

      if (!content || !knowledgeBase) {
        return this.createDefaultQualityResults();
      }

      // Calculate quality metrics
      this.updateProgress(context, 'Calculating quality metrics...', 98.5);
      const qualityMetrics = this.calculateQualityMetrics(content, knowledgeBase);

      // Generate citations
      const citations = this.generateCitations(knowledgeBase, content);

      // Perform fact-checking if enabled
      let factCheckResults: FactCheckResults | undefined;
      if (context.state.options.enableFactChecking) {
        this.updateProgress(context, 'Performing fact-checking...', 99);
        factCheckResults = await this.performFactChecking(content, knowledgeBase, context);
      }

      const result = {
        qualityMetrics,
        citations,
        factCheckResults
      };

      // Update state
      context.state.qualityMetrics = qualityMetrics;
      context.state.citations = citations;
      context.state.factCheckResults = factCheckResults;
      this.addResult(context, result);

      const overallScore = this.calculateOverallQualityScore(qualityMetrics);
      this.updateProgress(context, `Quality assurance completed - Score: ${Math.round(overallScore)}/100`, 100);

      context.services.logger.info('Quality assurance completed', {
        overallScore: Math.round(overallScore),
        readabilityScore: qualityMetrics.readabilityScore,
        factualAccuracy: qualityMetrics.factualAccuracy,
        citationsCount: citations.length,
        factCheckEnabled: !!factCheckResults
      });

      return result;

    } catch (error) {
      const errorMessage = `Quality assurance failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      this.addError(context, errorMessage);

      // Return default results
      const defaultResults = this.createDefaultQualityResults();
      context.state.qualityMetrics = defaultResults.qualityMetrics;
      context.state.citations = defaultResults.citations;

      this.updateProgress(context, 'Quality assurance completed with errors', 100);
      return defaultResults;
    }
  }

  private calculateQualityMetrics(content: string, knowledgeBase: any): QualityMetrics {
    const wordCount = content.split(' ').length;
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgWordsPerSentence = sentences.length > 0 ? wordCount / sentences.length : 0;

    const readabilityScore = this.calculateReadabilityScore(avgWordsPerSentence, content);
    const coherenceScore = this.calculateCoherenceScore(content);
    const factualAccuracy = this.calculateFactualAccuracy(content, knowledgeBase);
    const sourceReliability = this.calculateSourceReliabilityScore(knowledgeBase);
    const comprehensiveness = this.calculateComprehensiveness(content, knowledgeBase);
    const originalityScore = this.calculateOriginalityScore(content, knowledgeBase);

    const overallScore = this.calculateOverallQualityScore({
      readabilityScore,
      coherenceScore,
      factualAccuracy,
      sourceReliability,
      comprehensiveness,
      originalityScore,
      calculatedAt: new Date()
    });

    return {
      readabilityScore,
      coherenceScore,
      factualAccuracy,
      sourceReliability,
      comprehensiveness,
      originalityScore,
      overallScore,
      calculatedAt: new Date()
    };
  }

  private calculateReadabilityScore(avgWordsPerSentence: number, content: string): number {
    let score = 100;

    // Penalize long sentences
    if (avgWordsPerSentence > 25) {
      score -= 30;
    } else if (avgWordsPerSentence > 20) {
      score -= 15;
    }

    // Check for complex words (simplified approach)
    const complexWords = content.match(/\b\w{8,}\b/g) || [];
    const totalWords = content.split(' ').length;
    const complexWordRatio = totalWords > 0 ? complexWords.length / totalWords : 0;
    score -= complexWordRatio * 100;

    // Bonus for good structure
    if (content.includes('#') || content.includes('##')) {
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  private calculateCoherenceScore(content: string): number {
    let score = 60; // Base score

    // Check for structural elements
    const hasHeadings = /^#+\s/m.test(content);
    const hasTransitions = /\b(however|therefore|furthermore|moreover|additionally|consequently|meanwhile|similarly|in contrast|on the other hand)\b/i.test(content);
    const hasConclusion = /\b(conclusion|summary|in summary|to conclude|finally|in conclusion)\b/i.test(content);
    const hasIntroduction = content.length > 200; // Assume first part is introduction

    if (hasHeadings) score += 15;
    if (hasTransitions) score += 15;
    if (hasConclusion) score += 10;
    if (hasIntroduction) score += 5;

    // Check for logical flow indicators
    const flowIndicators = content.match(/\b(first|second|third|next|then|finally|lastly)\b/gi) || [];
    if (flowIndicators.length > 2) score += 10;

    return Math.min(100, score);
  }

  private calculateFactualAccuracy(content: string, knowledgeBase: any): number {
    const totalSources = knowledgeBase.totalSources;
    if (totalSources === 0) return 50;

    const allSources = [...knowledgeBase.primary, ...knowledgeBase.deep];
    const academicSources = allSources.filter(source => source.metadata.sourceType === 'academic').length;
    const officialSources = allSources.filter(source => source.metadata.sourceType === 'official').length;
    const reliableSources = academicSources + officialSources;

    const reliableSourceRatio = reliableSources / totalSources;
    const baseScore = 60 + reliableSourceRatio * 40;

    // Bonus for citing specific data or statistics
    const hasStatistics = /\b\d+%|\b\d+\s*(million|billion|thousand)|\$\d+/g.test(content);
    const statisticsBonus = hasStatistics ? 5 : 0;

    return Math.min(100, baseScore + statisticsBonus);
  }

  private calculateSourceReliabilityScore(knowledgeBase: any): number {
    const allSources = [...knowledgeBase.primary, ...knowledgeBase.deep];
    if (allSources.length === 0) return 50;

    const avgRelevanceScore = allSources.reduce((sum: number, source: any) => sum + source.relevanceScore, 0) / allSources.length;
    return Math.min(100, avgRelevanceScore * 100);
  }

  private calculateComprehensiveness(content: string, knowledgeBase: any): number {
    const contentLength = content.split(' ').length;
    const sourceCount = knowledgeBase.totalSources;

    // Base score on content length
    let score = Math.min(60, (contentLength / 2000) * 60);

    // Add points for source diversity
    score += Math.min(20, sourceCount * 3);

    // Bonus for covering multiple aspects (headings)
    const headings = content.match(/^#+\s/gm) || [];
    score += Math.min(20, headings.length * 3);

    return Math.min(100, score);
  }

  private calculateOriginalityScore(content: string, knowledgeBase: any): number {
    // Simplified originality check
    const contentWords = new Set(content.toLowerCase().match(/\b\w+\b/g) || []);
    const sourceWords = new Set();

    [...knowledgeBase.primary, ...knowledgeBase.deep].forEach((source: any) => {
      const words = source.content.toLowerCase().match(/\b\w+\b/g) || [];
      words.forEach((word: string) => sourceWords.add(word));
    });

    if (contentWords.size === 0) return 50;

    const uniqueWords = [...contentWords].filter(word => !sourceWords.has(word));
    const originalityRatio = uniqueWords.length / contentWords.size;

    return Math.min(100, 40 + originalityRatio * 60);
  }

  private generateCitations(knowledgeBase: any, content: string): SourceCitation[] {
    const allSources = [...knowledgeBase.primary, ...knowledgeBase.deep];

    return allSources.map((source: any) => ({
      id: source.id,
      url: source.url,
      title: source.title,
      domain: source.metadata.domain,
      citedIn: ['main-content'], // Simplified - would track actual citations in production
      relevanceScore: source.relevanceScore,
      sourceType: source.metadata.sourceType || 'unknown'
    }));
  }

  private async performFactChecking(content: string, knowledgeBase: any, context: AgentContext): Promise<FactCheckResults> {
    try {
      const totalSources = knowledgeBase.totalSources;
      const allSources = [...knowledgeBase.primary, ...knowledgeBase.deep];

      // Enhanced source reliability assessment
      const sourceReliabilityScores = this.calculateSourceReliability(allSources);
      const avgSourceReliability = sourceReliabilityScores.reduce((sum: number, score: number) => sum + score, 0) / sourceReliabilityScores.length;

      // Advanced fact-checking using AI
      const factCheckPrompt = this.buildFactCheckPrompt(content, allSources);

      const factCheckResult = await context.services.gemini.generateContent(factCheckPrompt, {
        temperature: 0.1, // Low temperature for factual accuracy
        maxOutputTokens: 2000
      });

      const parsedResults = this.parseFactCheckResults(factCheckResult);

      // Combine AI analysis with source reliability
      const trustworthiness = (avgSourceReliability * 0.6) + (parsedResults.aiConfidence * 0.4);

      return {
        verifiedClaims: parsedResults.verifiedClaims,
        unverifiedClaims: parsedResults.unverifiedClaims,
        disputedClaims: parsedResults.disputedClaims,
        overallTrustworthiness: Math.min(1, trustworthiness),
        checkedAt: new Date(),
        sources: allSources.map((source: any) => source.url).slice(0, 15)
      };
    } catch (error) {
      context.services.logger.error('Advanced fact-checking error', error);
      return this.getFallbackFactCheckResults(content, knowledgeBase);
    }
  }

  private calculateSourceReliability(sources: any[]): number[] {
    return sources.map(source => {
      let score = 0.5; // Base score

      // Source type scoring
      const sourceTypeScores = {
        'academic': 0.9,
        'official': 0.85,
        'news': 0.7,
        'blog': 0.4,
        'unknown': 0.3
      };
      score += (sourceTypeScores[source.metadata.sourceType as keyof typeof sourceTypeScores] || 0.3) * 0.4;

      // Domain authority (simplified)
      const domain = source.metadata.domain.toLowerCase();
      if (domain.includes('.edu') || domain.includes('.gov')) score += 0.2;
      if (domain.includes('wikipedia') || domain.includes('britannica')) score += 0.15;
      if (domain.includes('.org')) score += 0.1;

      // Content quality indicators
      if (source.metadata.wordCount > 1000) score += 0.1;
      if (source.relevanceScore > 0.7) score += 0.1;

      return Math.min(1, score);
    });
  }

  private buildFactCheckPrompt(content: string, sources: any[]): string {
    const sourcesSummary = sources.slice(0, 5).map((source, index) =>
      `Source ${index + 1}: ${source.title} (${source.metadata.domain})\nType: ${source.metadata.sourceType}\nContent: ${source.content.substring(0, 500)}...`
    ).join('\n\n');

    return `
Perform comprehensive fact-checking analysis on the following content:

CONTENT TO VERIFY:
${content.substring(0, 3000)}...

REFERENCE SOURCES:
${sourcesSummary}

Analyze the content for:

1. FACTUAL CLAIMS VERIFICATION
- Identify specific factual claims in the content
- Cross-reference each claim against the provided sources
- Categorize claims as: VERIFIED, UNVERIFIED, or DISPUTED

2. STATISTICAL ACCURACY
- Check all numbers, percentages, and statistics
- Verify data sources and methodology when mentioned
- Flag any inconsistencies or outdated information

3. LOGICAL CONSISTENCY
- Identify any internal contradictions
- Check for logical fallacies or unsupported conclusions
- Assess the strength of causal relationships claimed

4. SOURCE ATTRIBUTION
- Verify that claims are properly supported by sources
- Identify unsupported assertions
- Check for potential misrepresentation of source material

Provide your analysis in this format:
VERIFIED_CLAIMS: [number]
UNVERIFIED_CLAIMS: [number]
DISPUTED_CLAIMS: [number]
AI_CONFIDENCE: [0.0-1.0]
CRITICAL_ISSUES: [list any major factual concerns]
RECOMMENDATIONS: [suggestions for improvement]
    `;
  }

  private parseFactCheckResults(factCheckText: string): {
    verifiedClaims: number;
    unverifiedClaims: number;
    disputedClaims: number;
    aiConfidence: number;
  } {
    try {
      const verifiedMatch = factCheckText.match(/VERIFIED_CLAIMS:\s*(\d+)/i);
      const unverifiedMatch = factCheckText.match(/UNVERIFIED_CLAIMS:\s*(\d+)/i);
      const disputedMatch = factCheckText.match(/DISPUTED_CLAIMS:\s*(\d+)/i);
      const confidenceMatch = factCheckText.match(/AI_CONFIDENCE:\s*([\d.]+)/i);

      return {
        verifiedClaims: verifiedMatch ? parseInt(verifiedMatch[1]) : 0,
        unverifiedClaims: unverifiedMatch ? parseInt(unverifiedMatch[1]) : 0,
        disputedClaims: disputedMatch ? parseInt(disputedMatch[1]) : 0,
        aiConfidence: confidenceMatch ? parseFloat(confidenceMatch[1]) : 0.5
      };
    } catch (error) {
      return {
        verifiedClaims: 0,
        unverifiedClaims: 0,
        disputedClaims: 0,
        aiConfidence: 0.5
      };
    }
  }

  private getFallbackFactCheckResults(content: string, knowledgeBase: any): FactCheckResults {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const totalClaims = Math.min(sentences.length, 50); // Cap for fallback

    return {
      verifiedClaims: Math.floor(totalClaims * 0.6),
      unverifiedClaims: Math.floor(totalClaims * 0.3),
      disputedClaims: Math.floor(totalClaims * 0.1),
      overallTrustworthiness: 0.6,
      checkedAt: new Date(),
      sources: []
    };
  }

  private calculateOverallQualityScore(metrics: QualityMetrics): number {
    return (
      metrics.readabilityScore * 0.15 +
      metrics.coherenceScore * 0.20 +
      metrics.factualAccuracy * 0.25 +
      metrics.sourceReliability * 0.15 +
      metrics.comprehensiveness * 0.15 +
      metrics.originalityScore * 0.10
    );
  }

  private createDefaultQualityResults(): {
    qualityMetrics: QualityMetrics;
    citations: SourceCitation[];
    factCheckResults?: FactCheckResults;
  } {
    return {
      qualityMetrics: {
        readabilityScore: 75,
        coherenceScore: 70,
        factualAccuracy: 70,
        sourceReliability: 65,
        comprehensiveness: 70,
        originalityScore: 75,
        overallScore: 71,
        calculatedAt: new Date()
      },
      citations: []
    };
  }

  async validate(context: AgentContext): Promise<boolean> {
    if (!context.state.content) {
      context.services.logger.warn('QualityAssuranceAgent: No content available for quality check');
      // Don't fail validation - we can still provide default metrics
    }

    return true;
  }
}
