/**
 * Multi-Agent SuperAgent - Professional Content Writing Pipeline
 * 
 * This implementation uses a true multi-agent architecture where each agent
 * is an independent entity with its own capabilities, dependencies, and execution logic.
 * 
 * Architecture:
 * - 7 Independent Agents (Topic Analysis, Content Strategy, Primary Research, 
 *   Gap Analysis, Deep Research, Content Generation, Quality Assurance)
 * - Agent Coordinator for orchestration and communication
 * - Shared Services for common functionality
 * - Enhanced error handling and retry mechanisms
 * - Progress tracking and logging
 */

import { AgentCoordinator } from './multi-agent/agent-coordinator';
import { MultiAgentSharedServices } from './multi-agent/shared-services';
import { 
  SuperAgentState, 
  SuperAgentOptions, 
  WorkflowConfig,
  QualityMetrics,
  SourceCitation,
  KnowledgeBase
} from './multi-agent/types';

export class MultiAgentSuperAgent {
  private coordinator: AgentCoordinator;
  private services: MultiAgentSharedServices;
  private onProgress?: (phase: string, progress: number, message: string) => void;

  constructor(onProgress?: (phase: string, progress: number, message: string) => void) {
    this.onProgress = onProgress;
    this.services = new MultiAgentSharedServices();
    
    const config: WorkflowConfig = {
      enableParallelExecution: false, // Sequential execution for now
      maxConcurrentAgents: 1,
      retryAttempts: 3,
      timeoutMs: 300000, // 5 minutes per agent
      enableProgressTracking: true
    };

    this.coordinator = new AgentCoordinator(this.services, config, onProgress);
    
    this.services.logger.info('Multi-Agent SuperAgent initialized');
  }

  /**
   * Execute the complete multi-agent workflow
   */
  async executeWorkflow(topic: string, options: SuperAgentOptions = {}): Promise<MultiAgentResult> {
    const startTime = Date.now();
    
    this.services.logger.info('Starting multi-agent workflow execution', {
      topic,
      options
    });

    try {
      // Execute the workflow through the coordinator
      const state = await this.coordinator.executeWorkflow(topic, options);
      
      // Calculate execution time
      const executionTime = Date.now() - startTime;
      
      // Get execution statistics
      const stats = this.coordinator.getExecutionStats(state);
      
      // Build result
      const result: MultiAgentResult = {
        success: state.errors.length === 0 || this.hasMinimalRequiredResults(state),
        article: state.content || '',
        title: state.title || topic,
        wordCount: state.content?.split(' ').length || 0,
        qualityScore: this.calculateOverallQualityScore(state.qualityMetrics),
        sourcesUsed: state.knowledgeBase?.totalSources || 0,
        
        // Enhanced multi-agent specific data
        knowledgeBase: state.knowledgeBase,
        gapAnalysis: state.gapAnalysis,
        qualityMetrics: state.qualityMetrics,
        citations: state.citations || [],
        factCheckResults: state.factCheckResults,
        
        // Execution metadata
        executionTime,
        agentStats: stats,
        messages: state.messages,
        errors: state.errors,
        
        // Agent-specific results
        topicAnalysis: state.topicAnalysis,
        contentStrategy: state.contentStrategy,
        primaryResearch: state.primaryResearch,
        deepResearch: state.deepResearch,
        
        metadata: {
          workflow: 'multi-agent-superagent',
          timestamp: new Date().toISOString(),
          totalAgents: stats.totalAgents,
          successfulAgents: stats.successfulAgents,
          failedAgents: stats.failedAgents,
          retryAttempts: options.retryAttempts || 3,
          parallelExecution: false
        }
      };

      this.services.logger.info('Multi-agent workflow completed', {
        success: result.success,
        executionTime,
        wordCount: result.wordCount,
        sourcesUsed: result.sourcesUsed,
        qualityScore: result.qualityScore,
        agentStats: stats
      });

      return result;

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      this.services.logger.error('Multi-agent workflow failed', {
        error: errorMessage,
        executionTime,
        topic
      });

      // Return error result
      return {
        success: false,
        article: `# ${topic}\n\nMulti-agent workflow encountered an error: ${errorMessage}`,
        title: topic,
        wordCount: 0,
        qualityScore: 0,
        sourcesUsed: 0,
        executionTime,
        agentStats: {
          totalAgents: 7,
          successfulAgents: 0,
          failedAgents: 7,
          totalErrors: 1,
          executionSummary: {}
        },
        messages: [`Workflow failed: ${errorMessage}`],
        errors: [errorMessage],
        metadata: {
          workflow: 'multi-agent-superagent',
          timestamp: new Date().toISOString(),
          totalAgents: 7,
          successfulAgents: 0,
          failedAgents: 7,
          retryAttempts: options.retryAttempts || 3,
          parallelExecution: false
        }
      };
    }
  }

  /**
   * Check if we have minimal required results to consider the workflow successful
   */
  private hasMinimalRequiredResults(state: SuperAgentState): boolean {
    return !!(
      state.topicAnalysis && 
      state.content && 
      state.content.length > 100
    );
  }

  /**
   * Calculate overall quality score from quality metrics
   */
  private calculateOverallQualityScore(metrics?: QualityMetrics): number {
    if (!metrics) return 75; // Default score

    return metrics.overallScore || (
      metrics.readabilityScore * 0.15 +
      metrics.coherenceScore * 0.20 +
      metrics.factualAccuracy * 0.25 +
      metrics.sourceReliability * 0.15 +
      metrics.comprehensiveness * 0.15 +
      metrics.originalityScore * 0.10
    );
  }

  /**
   * Get agent execution statistics
   */
  getAgentStats(state: SuperAgentState) {
    return this.coordinator.getExecutionStats(state);
  }

  /**
   * Update workflow configuration
   */
  updateConfig(config: Partial<WorkflowConfig>): void {
    this.coordinator.updateConfig(config);
  }

  /**
   * Get available agent names
   */
  getAgentNames(): string[] {
    return this.coordinator.getAgentNames();
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    await this.coordinator.cleanup();
    this.services.logger.info('Multi-Agent SuperAgent cleaned up');
  }
}

/**
 * Result interface for multi-agent execution
 */
export interface MultiAgentResult {
  success: boolean;
  article: string;
  title: string;
  wordCount: number;
  qualityScore: number;
  sourcesUsed: number;
  executionTime: number;
  
  // Enhanced data
  knowledgeBase?: KnowledgeBase;
  gapAnalysis?: any;
  qualityMetrics?: QualityMetrics;
  citations?: SourceCitation[];
  factCheckResults?: any;
  
  // Agent results
  topicAnalysis?: any;
  contentStrategy?: any;
  primaryResearch?: any[];
  deepResearch?: any[];
  
  // Execution data
  agentStats: {
    totalAgents: number;
    successfulAgents: number;
    failedAgents: number;
    totalErrors: number;
    executionSummary: { [agentName: string]: { success: boolean; errors: number } };
  };
  messages: string[];
  errors: string[];
  
  metadata: {
    workflow: string;
    timestamp: string;
    totalAgents: number;
    successfulAgents: number;
    failedAgents: number;
    retryAttempts: number;
    parallelExecution: boolean;
  };
}

// Export types for external use
export type {
  SuperAgentOptions,
  SuperAgentState,
  WorkflowConfig,
  QualityMetrics,
  SourceCitation,
  KnowledgeBase
} from './multi-agent/types';
